﻿namespace IT.SharedLibraries.CT.CustomAttributes
{
    public static class CtOrderCustomAttributesNames
    {
        public static class Payment
        {
            public const string PROVIDER = "provider";
            public const string RESERVATION_ID = "reservationId";
            public const string LABEL = "label";
            public const string TRANSACTION_ID = "transactionId";
            // Swedish gift card payments
            public const string PROVIDER_RESPONSE = "providerResponse";
            public const string ID = "id";
            public const string REWARD_TYPE = "rewardType";

        }

        public static class CustomLinetItem
        {
            public const string SKU = "sku";
            public const string MARKETING_FEE = "marketingFee";
            public const string DESCRIPTION = "description";
            public const string IMAGE_URL = "image_url";
            // Swedish gift card payments
            public const string PROVIDER = "provider";
            public const string ID = "id";
            public const string REWARD_TYPE = "rewardType";
            public const string TRANSACTION_ID = "transactionId";
            public const string PROVIDER_RESPONSE = "providerResponse";

        }

        public static class ShippingAddress
        {
            public const string DATE = "date";
            public const string TIME = "time";
            public const string MOMENT = "moment";
            public const string CONTACT_PHONE = "contactPhone";
            public const string DELIVERY_MODE = "mode";
            //public const string BUILDING = "building";
            public const string LONGITUDE = "longitude";
            public const string LATITUDE = "latitude";
            public const string COMMENTS = "comments";
            public const string CONTACT_TITLE = "contactTitle";
            public const string CONTACT_FIRSTNAME = "contactFirstName";
            public const string CONTACT_LASTNAME = "contactLastName";
            public const string COMPANY_NUMBER = "companyNumber";
        }

        public static class BillingAddress
        {
            public const string FISCAL_CODE = "fiscalCode";
            public const string TYPE = "type";
            public const string COMPANY_NUMBER = "companyNumber";
            public const string PEC = "pec";
            public const string VAT_NUMBER = "vatNumber";
            public const string INVOICE_FIRSTNAME = "invoiceFirstName";
            public const string INVOICE_LASTNAME = "invoiceLastName";
            public const string INVOICE_REFERENCE = "invoiceReference";
            public const string COST_CENTER = "costCenter";
        }

        public static class Order
        {
            public const string MESSAGE = "message";
            public const string SIGNATURE = "signature";
            public const string USER_ID = "userId";
            public const string DEVICE = "device";
            public const string IP = "ip";
            public const string SESSION_ID = "sessionId";
            public const string INVOICE_REQUEST = "invoiceRequest";
            public const string INTERFLORA_PLUS = "interfloraplus";
            public const string LEGACY_ORDER_NUMBER = "legacyOrderNumber";
            public const string EXECUTING_FLORIST_ID = "executingFloristId";
            public const string EXECUTING_FLORIST_TYPE = "executingFloristType";
            public const string TRANSMITTER_FLORIST_ID = "transmitterFloristId";
            public const string EXECUTING_FLORIST_DELIVERY_AMOUNT = "executingFloristDeliveryAmount";
            public const string FLORIST_ORDER_STATUS = "floristOrderStatus";
            public const string INTERNAL_ORDER_ID = "internalOrderId";
            public const string EXTERNAL_COURIER_REQUESTED = "externalCourierRequested";
            public const string IS_MODIFIED = "isModified";
            public const string EXECUTING_FLORIST_INVOICE_URL = "executingFloristInvoiceUrl";

            public const string OCCASION_CODE = "occasionCode";
            public const string CART_ABANDONED_TAG = "cart_abandoned_tag";
            public const string DELIVERY_MODE = "deliveryMode";
            public const string SRC = "src";
            public const string TO_BE_ACCEPTED_BEFORE = "toBeAcceptedBefore";
            public const string READ_BY_EXECUTING_FLORIST = "readByExecutingFlorist";
            public const string DELIVERY_IN_PROGRESS = "deliveryInProgress";
            public const string DELIVERY_COST = "deliveryCost";
            public const string DELIVERY_DISTANCE = "deliveryDistance";
            public const string DELIVERY_SERVICE_REQUESTED = "deliveryServiceRequested";
            public const string DELIVERY_STATUS = "deliveryStatus";
            public const string DELIVERY_SERVICE = "deliveryService";
            public const string DELIVERY_SCHEDULED_DATE = "deliveryScheduledDate";
            public const string DELIVERY_TRACKING_CODE = "deliveryTrackingCode";

            public const string CONTACT_TITLE = "contactTitle";
            public const string CONTACT_FIRST_NAME = "contactFirstName";
            public const string CONTACT_LAST_NAME = "contactLastName";

            public const string NEW_ORDER_STATUS_CODE = "NEW_ORDER";
            public const string MORNING_DELIVERY_WINDOW_CODE = "M";
            public const string ORDER_STATUS_CODE_ASSIGNED = "ASSIGNED";
            public const string ORDER_STATUS_CODE_ACCEPTED = "ACCEPTED";
            public const string ORDER_STATUS_CODE_IN_DELIVERY = "IN_DELIVERY";
            public const string ORDER_STATUS_CODE_ABSENT = "ABSENT";
            public const string ORDER_STATUS_CODE_DELIVERED = "DELIVERED";
            public const string ORDER_STATUS_CODE_CANCELLED = "CANCELLED";
            public const string ORDER_SOURCE = "src";

            public const string VOUCHER = "voucher";
            public const string ADDITIONAL_DATA = "additional_data";
        }
        public static class LineItem
        {
            public const string RIBBON_TEXT = "ribbonText";
            public const string IS_ACCESSORY_FOR = "isAccessoryFor";
            public const string EXECUTING_FLORIST_AMOUNT = "executingFloristAmount";
            public const string MARKETING_FEE = "marketingFee";
            public const string COMPOSITION = "composition";
            public const string PRODUCT_TYPE_BUNDLE = "bundle";
            public const string BUNDELED_PRODUCTS_ATTRIBUTE_NAME = "bundled_products";
            public const string EXTRA_BUNDLE_INFOS = "extra_bundle_infos";
        }

        

        public const string LOCALIZED_ATTRIBUTE_KEY = "it";
    }
}
