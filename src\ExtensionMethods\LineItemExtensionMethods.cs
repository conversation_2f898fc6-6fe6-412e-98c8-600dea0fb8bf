﻿using commercetools.Sdk.Api.Models.Carts;
using commercetools.Sdk.Api.Models.Common;
using IT.SharedLibraries.CT.CustomAttributes;
using ITF.SharedModels.Group.Enums;
using Newtonsoft.Json;
using static ITF.SharedModels.Notifications.Business.Legacy.Payloads.Common;

namespace IT.SharedLibraries.CT.ExtensionMethods
{
    public class ExtraBundleInfo
    {
        [JsonProperty("bundleCode")]
        public string BundleCode {  get; set; }
        [JsonProperty("bundleVariantCodes")]
        public string BundleVariantCodes { get; set; }
    }

    public static class LineItemExtensionMethods
    {

        public static ExtraBundleInfo? GetExtraBundleInfo(this ILineItem li)
        {
            ExtraBundleInfo? obj = null;
            if (li.Custom != null && li.Custom.Fields.TryGetValue(CtOrderCustomAttributesNames.LineItem.EXTRA_BUNDLE_INFOS, out object? value))
            {
                string fieldValue = value?.ToString();
                obj = JsonConvert.DeserializeObject<ExtraBundleInfo>(fieldValue);
            }
            return obj;
        }
        public static string? GetIsAccessoryFor(this ILineItem li)
        {
            if (li.Custom != null && li.Custom.Fields.TryGetValue(CtOrderCustomAttributesNames.LineItem.IS_ACCESSORY_FOR, out object? value))
                return value?.ToString();
            return null;
        }
        public static string? GetRibbonText(this ILineItem li)
        {
            if (li.Custom != null && li.Custom.Fields.TryGetValue(CtOrderCustomAttributesNames.LineItem.RIBBON_TEXT, out object? value))
                return value?.ToString();
            return null;
        }
        public static string? GetComposition(this ILineItem li)
        {
            if (li.Custom != null && li.Custom.Fields.TryGetValue(CtOrderCustomAttributesNames.LineItem.COMPOSITION, out object? value))
                return value?.ToString();
            return null;
        }

        public static decimal GetExecutingFloristAmount(this ILineItem li)
        {
            decimal amount = 0;
            if (li.Custom != null && li.Custom.Fields.TryGetValue(CtOrderCustomAttributesNames.LineItem.EXECUTING_FLORIST_AMOUNT, out object? value))
            {
                var money = (commercetools.Sdk.Api.Models.Common.CentPrecisionMoney)value;
                amount = money != null ? money.CentAmount / (decimal)Math.Pow(10, money.FractionDigits) : 0;
            }
            return amount;
        }

        public static decimal GetMarketingFee(this ILineItem li)
        {
            decimal amount = 0;
            if (li.Custom != null && li.Custom.Fields.TryGetValue(CtOrderCustomAttributesNames.LineItem.MARKETING_FEE, out object? value))
            {
                var money = (commercetools.Sdk.Api.Models.Common.CentPrecisionMoney)value;
                amount = money != null ? money.CentAmount / (decimal)Math.Pow(10, money.FractionDigits) : 0;
            }
            return amount;
        }

        public static decimal GetPrice(this ILineItem li, FloristTypeEnum floristType = FloristTypeEnum.Unknown)
        {
            var marketingFee = li.GetMarketingFee();
            var discountedValue = li.Price.Discounted?.Value?.AmountToDecimal();
            var baseValue = li.Price.Value.AmountToDecimal();
            var quantity = li.Quantity;

            if (floristType == FloristTypeEnum.Executor)
            {
                var executingFloristAmount = li.GetExecutingFloristAmount();
                if (executingFloristAmount > 0)
                {
                    return executingFloristAmount;
                }

                return (discountedValue ?? baseValue) * quantity - marketingFee;
            }

            return (discountedValue ?? baseValue) * quantity;
        }

        public static decimal GetBasePrice(this ILineItem li) => li.Price.Value.AmountToDecimal() * li.Quantity;


        public static ProductInformations MapLineItemToRAOLineItem(this ILineItem li)
        {
            var variantParts = li.Variant?.Key?.Split('-') ?? Array.Empty<string>();
            string size = string.Empty;
            string style = string.Empty;

            if (variantParts.Length == 3)
            {
                size = variantParts[1];    
                style = variantParts[2];   
            }

            var marketingFee = li.GetMarketingFee();
            return new ProductInformations
            {
                ProductId = li.ProductKey,
                Quantity = (int)li.Quantity,
                Margin = Convert.ToDouble(marketingFee),
                Privex = Convert.ToDouble(li.GetExecutingFloristAmount()),
                RibbonText = li.GetRibbonText() ?? "",
                Price = Convert.ToDouble(li.GetPrice()),
                Label = li.Name.ContainsKey("fr") ? li.Name["fr"] : string.Empty,
                Style = style,
                Size = size,
                IntercatCode = li.Variant?.Key ?? string.Empty,
            };
        }

        public static bool IsUpdateNeeded(this ILineItem li, ProductInformations product)
        {

            return product.Privex != Convert.ToDouble(li.GetExecutingFloristAmount()) ||
               !(product.RibbonText ?? string.Empty).Equals(li.GetRibbonText() ?? string.Empty, StringComparison.OrdinalIgnoreCase) ||
                   product.Margin != Convert.ToDouble(li.GetMarketingFee()) ||
                   product.Price * product.Quantity != Convert.ToDouble(li.GetPrice()) ||
                   !(product.Description?.Trim() ?? string.Empty).Equals(li.GetComposition()?.Trim() ?? string.Empty, StringComparison.OrdinalIgnoreCase);

        }

        public static bool IsEligible(this ILineItem lineItem)
        {
            return lineItem.ProductKey != "ITFPLUS";
        }

        public static bool IsBundle(this ILineItem lineItem)
        {
            return lineItem.Variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtOrderCustomAttributesNames.LineItem.BUNDELED_PRODUCTS_ATTRIBUTE_NAME)) is not null;
        }
        public static List<IReference>? GetBundleProductsReferences(this ILineItem lineItem)
        {     
            var firstOrDefault = lineItem.Variant.Attributes.FirstOrDefault(a => a.Name.ToLower().Equals(CtOrderCustomAttributesNames.LineItem.BUNDELED_PRODUCTS_ATTRIBUTE_NAME));
            if (firstOrDefault != null)
            {
                object bundled_products = firstOrDefault.Value;
                if (bundled_products is List<IReference> result)
                    return result;
            }
            return null;
        }

    }

    public static class LineItemsListExtensionMethods
    {
        public static List<ProductInformations> MapOrderLineItemsToRAOLineItems(this IList<ILineItem> lineItems)
        {
            List<ProductInformations> list = new();

            foreach (ILineItem lineItem in lineItems)
            {
                

            }
            return list;
        }
    }
}
