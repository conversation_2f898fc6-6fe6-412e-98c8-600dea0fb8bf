﻿using commercetools.Base.Serialization;
using commercetools.Sdk.Api.Models.Messages;
using commercetools.Sdk.Api.Models.OrderEdits;
using commercetools.Sdk.Api.Models.Orders;
using commercetools.Sdk.Api.Serialization;
using IT.Microservices.OrderReactor.Domain;
using IT.Microservices.OrderReactor.Infrastructure;
using IT.Microservices.OrderReactor.Infrastructure.Settings;
using IT.SharedLibraries.CT.CustomAttributes;
using IT.SharedLibraries.CT.ExtensionMethods;
using IT.SharedLibraries.CT.Orders;
using IT.SharedLibraries.CT.Orders.Comparers;
using IT.SharedLibraries.CT.Repository;
using IT.SharedLibraries.CT.Settings;
using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedLibraries.Alerting;
using ITF.SharedLibraries.ExtensionMethods;
using ITF.SharedModels.DataModels.Florist;
using ITF.SharedModels.DataModels.Order;
using ITF.SharedModels.Group.Enums;
using ITF.SharedModels.Messages.Group.Order;
using ITF.SharedModels.Messages.Italy.Order.Legacy;
using ITF.SharedModels.Notifications.Business.Legacy.Payloads;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Yggdrasil;
using static ITF.SharedLibraries.ExtensionMethods.Serializer;
using static ITF.SharedModels.Messages.France.Order.Messages.V1;
using static ITF.SharedModels.Messages.Italy.Order.Legacy.Messages.V1;
using static ITF.SharedModels.Notifications.Business.Legacy.Messages.Messages.V1;
using static ITF.SharedModels.Notifications.Business.Legacy.Payloads.Common;


namespace IT.Microservices.OrderReactor.Application
{
    public class OrderUseCase : IOrderUseCase
    {
        private readonly ILogger<OrderUseCase> logger;
        private readonly IOrderService orderService;
        private readonly IFloristOrderPerDayRepository iFloristOrderPerDayRepository;
        private readonly IFloristsRepository iFloristsRepository;
        private readonly ISequenceGeneratorService sequenceGeneratorService;
        private readonly IKafkaPublisherHelper kafkaPublisher;
        private readonly IOrderLineItemsPricesRepository? _orderLineItemsPricesRepository;
        private readonly IOrderClaimsRepository _orderClaimsRepository;
        private readonly IOrderCustomerFeedbackRepository _orderCustomerFeedbackRepository;
        private readonly IOptionsMonitor<KafkaTopicsSettings> kafkaTopicsSettings;
        private readonly IOptionsMonitor<OrderReactorSettings> orderReactorSettings;
        private readonly SerializerService serializerService;
        private readonly ISlackAlertService? slackAlertService;
        private readonly RetryHandler retryHandler;

        public OrderUseCase(ILogger<OrderUseCase> logger, IOrderService orderService,
            IFloristOrderPerDayRepository iFloristOrderPerDayRepository, IFloristsRepository iFloristsRepository, IOrderClaimsRepository orderClaimsRepository, IOrderCustomerFeedbackRepository orderCustomerFeedbackRepository, ISequenceGeneratorService sequenceGeneratorService,
            IKafkaPublisherHelper kafkaPublisher, IOptionsMonitor<KafkaTopicsSettings> kafkaTopicsSettings, IOptionsMonitor<CommerceToolCustomSettings> commerceToolCustomSettings,
            IOptionsMonitor<OrderReactorSettings> orderReactorSettings, SerializerService serializerService, IOrderLineItemsPricesRepository? orderLineItemsPricesRepository = null, ISlackAlertService? slackAlertService = null)
        {
            this.logger = logger;
            this.orderService = orderService;
            this.iFloristOrderPerDayRepository = iFloristOrderPerDayRepository;
            this.iFloristsRepository = iFloristsRepository;
            this.sequenceGeneratorService = sequenceGeneratorService;
            this.kafkaPublisher = kafkaPublisher;
            this.kafkaTopicsSettings = kafkaTopicsSettings;
            this.orderReactorSettings = orderReactorSettings;
            this.serializerService = serializerService;
            this.slackAlertService = slackAlertService;
            this._orderClaimsRepository = orderClaimsRepository;
            this._orderCustomerFeedbackRepository = orderCustomerFeedbackRepository;

            // Configure retry settings
            var retryConfig = new RetryConfiguration
            {
                MaxAttempts = 5,           // Try up to 5 times
                BaseDelayMs = 100,         // Start with 100ms delay
                UseExponentialBackoff = true,
                MaxDelayMs = 2000,         // Max 2 seconds delay
                BackoffMultiplier = 1.5,   // 1.5x increase each retry
                UseJitter = true           // Add randomness to prevent thundering herd
            };

            retryHandler = new RetryHandler(logger, retryConfig);
        }

        public async Task SychronizeProcess(LegacyOrderCreatedMessage message)
        {
            try
            {
                logger.LogInformation("{Message} : Synchronized {Name}", nameof(LegacyOrderCreatedMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }

                var order = await CreateOrderProcess(message);

            }
            catch (commercetools.Base.Client.Error.BadRequestException brex)
            {
                bool rethrow = true;
                if (brex.ResponseBody != null && brex.ResponseBody is commercetools.Sdk.Api.Models.Errors.ErrorResponse)
                {
                    var errorResponse = brex.ResponseBody as commercetools.Sdk.Api.Models.Errors.ErrorResponse;
                    if (errorResponse.Errors.Any(e => e is commercetools.Sdk.Api.Models.Errors.DuplicateFieldError && (e as commercetools.Sdk.Api.Models.Errors.DuplicateFieldError).Code == "DuplicateField" && (e as commercetools.Sdk.Api.Models.Errors.DuplicateFieldError).Field == "orderNumber"))
                    {
                        logger.LogWarning(brex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Reason: order with same orderNumber already exists in CT", nameof(SychronizeProcess), nameof(LegacyOrderCreatedMessage), message.GetMessageKey(), message.Serialize());
                        rethrow = false;
                    }
                }

                if (rethrow)
                {
                    logger.LogWarning(brex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Message: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderCreatedMessage), message.GetMessageKey(), message.Serialize(), brex.ToString());
                    await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderCreatedMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {brex}", brex) ?? Task.CompletedTask);

                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderCreatedMessage), message.GetMessageKey(), message.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderCreatedMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(LegacyOrderAssignedMessage message)
        {
            try
            {
                logger.LogInformation("{Message} : Synchronized {Name}", nameof(LegacyOrderAssignedMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }

                await AssignOrderProcess(message);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderAssignedMessage), message.GetMessageKey(), message.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderAssignedMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(LegacyOrderCancelledMessage message)
        {
            try
            {
                logger.LogInformation("{Message} : Synchronized {Name}", nameof(LegacyOrderCancelledMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }

                GlobalOrderCancelled orderCancelled = message;
                IOrder orderBeforeUpdate = await retryHandler.Handle409Conflict(orderService.HandleOrderCancelled, orderCancelled, nameof(LegacyOrderCancelledMessage));
                if (orderBeforeUpdate != null)
                {
                    OrderNewHistoryRecordMessageBuilder builder = new OrderNewHistoryRecordMessageBuilder();
                    builder.AddCommerceToolsID(message.Payload.OrderIdentifier)
                        .AddOrderNumber(orderBeforeUpdate.OrderNumber)
                        .AddLegacyOrderNumber(orderBeforeUpdate.GetLegacyOrderNumber())
                        .AddInitialOrderStatus(orderBeforeUpdate.GetFloristOrderStatus())
                        .AddExecutingFloristId(orderBeforeUpdate.GetExecutingFloristId())
                        .AddOrderAmount(orderBeforeUpdate.GetTotalItemsPrice() + orderBeforeUpdate.GetDeliveryPrice())
                        .AddRequest(message.Serialize())
                        .AddOrderAction("CANCELLED DONE")
                        .AddCtOrderPreUpdate(orderBeforeUpdate?.Serialize(SerializerType.CommerceTools, serializerService))
                        .AddMessage($"Order {orderBeforeUpdate.OrderNumber} has been updated as CANCELLED")
                        .Build();
                    await kafkaPublisher.Publish(builder.Build(), kafkaTopicsSettings.CurrentValue.Order);

                    logger.LogInformation($"Order with id={message?.Payload?.OrderIdentifier} status updated to CANCELLED");
                }
                else
                {
                    logger.LogWarning($"Order with id={message?.Payload?.OrderIdentifier} not found");
                }

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderCancelledMessage), message.GetMessageKey(), message.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderCancelledMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(LegacyOrderDeliveryTimeUpdatedMessage message)
        {
            try
            {
                logger.LogInformation("{Message} : Synchronized {Name}", nameof(LegacyOrderDeliveryTimeUpdatedMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }

                GlobalOrderDeliveryTimeUpdated orderDeliveryTimeUpdated = message;
                IOrder orderBeforeUpdate = await retryHandler.Handle409Conflict(orderService.HandleOrderDeliveryTimeUpdated, orderDeliveryTimeUpdated, nameof(LegacyOrderDeliveryTimeUpdatedMessage));
                if (orderBeforeUpdate != null)
                {
                    OrderNewHistoryRecordMessageBuilder builder = new OrderNewHistoryRecordMessageBuilder();
                    builder.AddCommerceToolsID(message.Payload.OrderIdentifier)
                        .AddOrderNumber(orderBeforeUpdate.OrderNumber)
                        .AddLegacyOrderNumber(orderBeforeUpdate.GetLegacyOrderNumber())
                        .AddInitialOrderStatus(orderBeforeUpdate.GetFloristOrderStatus())
                        .AddExecutingFloristId(orderBeforeUpdate.GetExecutingFloristId())
                        .AddOrderAmount(orderBeforeUpdate.GetTotalItemsPrice() + orderBeforeUpdate.GetDeliveryPrice())
                        .AddRequest(message.Serialize())
                        .AddOrderAction("DELIVERY TIME UPDATED DONE")
                        .AddCtOrderPreUpdate(orderBeforeUpdate?.Serialize(SerializerType.CommerceTools, serializerService))
                        .AddMessage($"Delivery time of the order {orderBeforeUpdate.OrderNumber} has been updated to {orderDeliveryTimeUpdated.Moment} / {orderDeliveryTimeUpdated.Time ?? "null"}")
                        .Build();
                    await kafkaPublisher.Publish(builder.Build(), kafkaTopicsSettings.CurrentValue.Order);

                    logger.LogInformation($"Order with id={message?.Payload?.OrderIdentifier} delivery times updated");
                }
                else
                {
                    logger.LogWarning($"Order with id={message?.Payload?.OrderIdentifier} not found");
                }

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderDeliveryTimeUpdatedMessage), message.GetMessageKey(), message.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderDeliveryTimeUpdatedMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(LegacyOrderDeliveryStatusUpdatedMessage message)
        {
            try
            {
                logger.LogInformation("{Message} : Synchronized {Name}", nameof(LegacyOrderDeliveryStatusUpdatedMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }

                GlobalOrderDeliveryStatusUpdated orderDeliveryStatusUpdated = message;
                var order = await orderService.GetByOrderNumber(message?.Payload?.OrderIdentifier);// handle some case when orderNumber is provided instead of orderCtId which is the one required for that process 
                if (order != null)
                    orderDeliveryStatusUpdated.OrderIdentifier = order.Id;

                IOrder orderBeforeUpdate = await retryHandler.Handle409Conflict(orderService.HandleOrderDeliveryStatusUpdated, orderDeliveryStatusUpdated, nameof(LegacyOrderDeliveryStatusUpdatedMessage));
                if (orderBeforeUpdate != null)
                {
                    OrderNewHistoryRecordMessageBuilder builder = new OrderNewHistoryRecordMessageBuilder();
                    builder.AddCommerceToolsID(message.Payload.OrderIdentifier)
                        .AddOrderNumber(orderBeforeUpdate.OrderNumber)
                        .AddLegacyOrderNumber(orderBeforeUpdate.GetLegacyOrderNumber())
                        .AddInitialOrderStatus(orderBeforeUpdate.GetFloristOrderStatus())
                        .AddExecutingFloristId(orderBeforeUpdate.GetExecutingFloristId())
                        .AddOrderAmount(orderBeforeUpdate.GetTotalItemsPrice() + orderBeforeUpdate.GetDeliveryPrice())
                        .AddRequest(message.Serialize())
                        .AddOrderAction(orderDeliveryStatusUpdated.DeliveryStatus == DeliveryStatusEnum.DELIVERED ? "DELIVERED DONE" : "DELIVERY STATUS UPDATED DONE")
                        .AddCtOrderPreUpdate(orderBeforeUpdate?.Serialize(SerializerType.CommerceTools, serializerService))
                        .AddMessage($"Delivery status of the order {orderBeforeUpdate.OrderNumber} has been updated to {orderDeliveryStatusUpdated.DeliveryStatus}")
                        .Build();
                    await kafkaPublisher.Publish(builder.Build(), kafkaTopicsSettings.CurrentValue.Order);

                    logger.LogInformation($"Order with id={message?.Payload?.OrderIdentifier} delivery status updated");
                }
                else
                {
                    logger.LogWarning($"Order with id={message?.Payload?.OrderIdentifier} not found");
                }

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderDeliveryStatusUpdatedMessage), message.GetMessageKey(), message.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderDeliveryStatusUpdatedMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(LegacyOrderDeliveryCostUpdatedMessage message)
        {
            try
            {
                logger.LogInformation("{Message} : Synchronized {Name}", nameof(LegacyOrderDeliveryCostUpdatedMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }

                GlobalOrderDeliveryCostUpdated orderDeliveryCostUpdated = message;
                var order = await orderService.GetByOrderNumber(message?.Payload?.OrderIdentifier);// handle some case when orderNumber is provided instead of orderCtId which is the one required for that process 
                if (order != null)
                    orderDeliveryCostUpdated.OrderIdentifier = order.Id;

                IOrder orderBeforeUpdate = await retryHandler.Handle409Conflict(orderService.HandleOrderDeliveryCostUpdated, orderDeliveryCostUpdated, nameof(LegacyOrderDeliveryCostUpdatedMessage));
                if (orderBeforeUpdate != null)
                {
                    OrderNewHistoryRecordMessageBuilder builder = new OrderNewHistoryRecordMessageBuilder();
                    builder.AddCommerceToolsID(message.Payload.OrderIdentifier)
                        .AddOrderNumber(orderBeforeUpdate.OrderNumber)
                        .AddLegacyOrderNumber(orderBeforeUpdate.GetLegacyOrderNumber())
                        .AddInitialOrderStatus(orderBeforeUpdate.GetFloristOrderStatus())
                        .AddExecutingFloristId(orderBeforeUpdate.GetExecutingFloristId())
                        .AddOrderAmount(orderBeforeUpdate.GetTotalItemsPrice() + orderBeforeUpdate.GetDeliveryPrice())
                        .AddRequest(message.Serialize())
                        .AddOrderAction("DELIVERY COST UPDATED DONE")
                        .AddCtOrderPreUpdate(orderBeforeUpdate?.Serialize(SerializerType.CommerceTools, serializerService))
                        .AddMessage($"Delivery cost of the order {orderBeforeUpdate.OrderNumber} has been updated to {orderDeliveryCostUpdated.DeliveryCost}")
                        .Build();
                    await kafkaPublisher.Publish(builder.Build(), kafkaTopicsSettings.CurrentValue.Order);

                    logger.LogInformation($"Order with id={message?.Payload?.OrderIdentifier} delivery cost updated");
                }
                else
                {
                    logger.LogWarning($"Order with id={message?.Payload?.OrderIdentifier} not found");
                }

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderDeliveryCostUpdatedMessage), message.GetMessageKey(), message.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderDeliveryCostUpdatedMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(LegacyOrderDeliveryDateUpdatedMessage message)
        {
            try
            {
                logger.LogInformation("{Message} : Synchronized {Name}", nameof(LegacyOrderDeliveryDateUpdatedMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }
                GlobalOrderDeliveryDateUpdated orderDeliveryDateUpdated = message;
                IOrder orderBeforeUpdate = await retryHandler.Handle409Conflict(orderService.HandleOrderDeliveryDateUpdated, orderDeliveryDateUpdated, nameof(LegacyOrderDeliveryDateUpdatedMessage));
                if (orderBeforeUpdate != null)
                {
                    OrderNewHistoryRecordMessageBuilder builder = new OrderNewHistoryRecordMessageBuilder();
                    builder.AddCommerceToolsID(message.Payload.OrderIdentifier)
                        .AddOrderNumber(orderBeforeUpdate.OrderNumber)
                        .AddLegacyOrderNumber(orderBeforeUpdate.GetLegacyOrderNumber())
                        .AddInitialOrderStatus(orderBeforeUpdate.GetFloristOrderStatus())
                        .AddExecutingFloristId(orderBeforeUpdate.GetExecutingFloristId())
                        .AddOrderAmount(orderBeforeUpdate.GetTotalItemsPrice() + orderBeforeUpdate.GetDeliveryPrice())
                        .AddRequest(message.Serialize())
                        .AddOrderAction("DELIVERY DATE UPDATED DONE")
                        .AddCtOrderPreUpdate(orderBeforeUpdate?.Serialize(SerializerType.CommerceTools, serializerService))
                        .AddMessage($"Delivery date of the order {orderBeforeUpdate.OrderNumber} has been updated to {orderDeliveryDateUpdated.Date.ToString("yyyyMMdd")}")
                        .Build();
                    await kafkaPublisher.Publish(builder.Build(), kafkaTopicsSettings.CurrentValue.Order);

                    logger.LogInformation("Order with id={orderId} delivery date updated", message.Payload?.OrderIdentifier);
                }
                else
                {
                    logger.LogWarning("Order with id={orderId} not found", message.Payload?.OrderIdentifier);
                }

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderDeliveryDateUpdatedMessage), message.GetMessageKey(), message.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderDeliveryDateUpdatedMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(LegacyOrderCardMessageUpdatedMessage message)
        {
            try
            {
                logger.LogInformation("{Message} : Synchronized {Name}", nameof(LegacyOrderCardMessageUpdatedMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }
                GlobalOrderCardMessageUpdated orderCardMessageUpdated = message;
                IOrder orderBeforeUpdate = await retryHandler.Handle409Conflict(orderService.HandleOrderCardMessageUpdated, orderCardMessageUpdated, nameof(LegacyOrderCardMessageUpdatedMessage));
                if (orderBeforeUpdate != null)
                {
                    OrderNewHistoryRecordMessageBuilder builder = new OrderNewHistoryRecordMessageBuilder();
                    builder.AddCommerceToolsID(message.Payload.OrderIdentifier)
                        .AddOrderNumber(orderBeforeUpdate.OrderNumber)
                        .AddLegacyOrderNumber(orderBeforeUpdate.GetLegacyOrderNumber())
                        .AddInitialOrderStatus(orderBeforeUpdate.GetFloristOrderStatus())
                        .AddExecutingFloristId(orderBeforeUpdate.GetExecutingFloristId())
                        .AddOrderAmount(orderBeforeUpdate.GetTotalItemsPrice() + orderBeforeUpdate.GetDeliveryPrice())
                        .AddRequest(message.Serialize())
                        .AddOrderAction("CARD MESSAGE UPDATED DONE")
                        .AddCtOrderPreUpdate(orderBeforeUpdate?.Serialize(SerializerType.CommerceTools, serializerService))
                        .AddMessage($"Card Message of the order {orderBeforeUpdate.OrderNumber} has been updated")
                        .Build();
                    await kafkaPublisher.Publish(builder.Build(), kafkaTopicsSettings.CurrentValue.Order);

                    logger.LogInformation("Order with id={orderId} card message updated", message.Payload?.OrderIdentifier);
                }
                else
                {
                    logger.LogWarning("Order with id={orderId} not found", message.Payload?.OrderIdentifier);
                }

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderCardMessageUpdatedMessage), message.GetMessageKey(), message.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderCardMessageUpdatedMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(LegacyOrderNotesUpdatedMessage message)
        {
            try
            {
                logger.LogInformation("{Message} : Synchronized {Name}", nameof(LegacyOrderNotesUpdatedMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }
                GlobalOrderNotesUpdated orderNotesUpdated = message;
                IOrder orderBeforeUpdate = await retryHandler.Handle409Conflict(orderService.HandleOrderNotesUpdated, orderNotesUpdated, nameof(LegacyOrderNotesUpdatedMessage));
                if (orderBeforeUpdate != null)
                {

                    OrderNewHistoryRecordMessageBuilder builder = new OrderNewHistoryRecordMessageBuilder();
                    builder.AddCommerceToolsID(message.Payload.OrderIdentifier)
                        .AddOrderNumber(orderBeforeUpdate.OrderNumber)
                        .AddLegacyOrderNumber(orderBeforeUpdate.GetLegacyOrderNumber())
                        .AddInitialOrderStatus(orderBeforeUpdate.GetFloristOrderStatus())
                        .AddExecutingFloristId(orderBeforeUpdate.GetExecutingFloristId())
                        .AddOrderAmount(orderBeforeUpdate.GetTotalItemsPrice() + orderBeforeUpdate.GetDeliveryPrice())
                        .AddRequest(message.Serialize())
                        .AddOrderAction("NOTES UPDATED DONE")
                        .AddCtOrderPreUpdate(orderBeforeUpdate?.Serialize(SerializerType.CommerceTools, serializerService))
                        .AddMessage($"Notes of the order {orderBeforeUpdate.OrderNumber} has been updated")
                        .Build();
                    await kafkaPublisher.Publish(builder.Build(), kafkaTopicsSettings.CurrentValue.Order);

                    logger.LogInformation($"Order with id={message?.Payload?.OrderIdentifier} notes updated");
                }
                else
                {
                    logger.LogWarning($"Order with id={message?.Payload?.OrderIdentifier} not found");
                }

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderNotesUpdatedMessage), message.GetMessageKey(), message.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderNotesUpdatedMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(LegacyOrderDeliveryAddressUpdatedMessage message)
        {
            try
            {
                logger.LogInformation("{Message} : Synchronized {Name}", nameof(LegacyOrderDeliveryAddressUpdatedMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }
                GlobalOrderDeliveryAddressUpdated orderDeliveryAddressUpdated = message;

                IOrder orderBeforeUpdate = await retryHandler.Handle409Conflict(orderService.HandleOrderDeliveryAddressUpdated, orderDeliveryAddressUpdated, nameof(LegacyOrderDeliveryAddressUpdatedMessage));
                if (orderBeforeUpdate != null)
                {
                    OrderNewHistoryRecordMessageBuilder builder = new OrderNewHistoryRecordMessageBuilder();
                    builder.AddCommerceToolsID(message.Payload.OrderIdentifier)
                        .AddOrderNumber(orderBeforeUpdate.OrderNumber)
                        .AddLegacyOrderNumber(orderBeforeUpdate.GetLegacyOrderNumber())
                        .AddInitialOrderStatus(orderBeforeUpdate.GetFloristOrderStatus())
                        .AddExecutingFloristId(orderBeforeUpdate.GetExecutingFloristId())
                        .AddOrderAmount(orderBeforeUpdate.GetTotalItemsPrice() + orderBeforeUpdate.GetDeliveryPrice())
                        .AddRequest(message.Serialize())
                        .AddOrderAction("DELIVERY ADDRESS UPDATED DONE")
                        .AddCtOrderPreUpdate(orderBeforeUpdate?.Serialize(SerializerType.CommerceTools, serializerService))
                        .AddMessage($"Delivery address of the order {orderBeforeUpdate.OrderNumber} has been updated")
                        .Build();
                    await kafkaPublisher.Publish(builder.Build(), kafkaTopicsSettings.CurrentValue.Order);

                    logger.LogInformation($"Order with id={message?.Payload?.OrderIdentifier} delivery address updated");
                }
                else
                {
                    logger.LogWarning($"Order with id={message?.Payload?.OrderIdentifier} not found");
                }

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderDeliveryAddressUpdatedMessage), message.GetMessageKey(), message.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderDeliveryAddressUpdatedMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(LegacyOrderDeliveredOnBehalfMessage message)
        {
            try
            {
                logger.LogInformation("{Message} : Synchronized {Name}", nameof(LegacyOrderDeliveredOnBehalfMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }

                GlobalOrderDeliveredOnBehalf globalOrderDelivered = message;

                IOrder orderBeforeUpdate = await retryHandler.Handle409Conflict(orderService.HandleOrderDeliveredOnBehalf, globalOrderDelivered, nameof(LegacyOrderDeliveredOnBehalfMessage));
                if (orderBeforeUpdate != null)
                {
                    OrderNewHistoryRecordMessageBuilder builder = new OrderNewHistoryRecordMessageBuilder();
                    builder.AddCommerceToolsID(message.Payload.OrderIdentifier)
                        .AddOrderNumber(orderBeforeUpdate.OrderNumber)
                        .AddLegacyOrderNumber(orderBeforeUpdate.GetLegacyOrderNumber())
                        .AddInitialOrderStatus(orderBeforeUpdate.GetFloristOrderStatus())
                        .AddExecutingFloristId(orderBeforeUpdate.GetExecutingFloristId())
                        .AddOrderAmount(orderBeforeUpdate.GetTotalItemsPrice() + orderBeforeUpdate.GetDeliveryPrice())
                        .AddRequest(message.Serialize())
                        //.AddOrderAction("DELIVERED ON BEHALF DONE")
                        .AddOrderAction("DELIVERED DONE")
                        .AddCtOrderPreUpdate(orderBeforeUpdate?.Serialize(SerializerType.CommerceTools, serializerService))
                        .AddMessage($"The order {orderBeforeUpdate.OrderNumber} has been updated as DELIVERED on behalf")
                        .Build();
                    var orderNewHistoryRecordMessage = builder.Build();

                    await kafkaPublisher.Publish(orderNewHistoryRecordMessage, kafkaTopicsSettings.CurrentValue.Order);

                    logger.LogInformation($"Order with id={message?.Payload?.OrderIdentifier} delivered on behalf, enqueued: {Newtonsoft.Json.JsonConvert.SerializeObject(orderNewHistoryRecordMessage)}");
                }
                else
                {
                    logger.LogWarning($"Order with id={message?.Payload?.OrderIdentifier} not found");
                }

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderDeliveredOnBehalfMessage), message.GetMessageKey(), message.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderDeliveredOnBehalfMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(LegacyOrderAcceptedMessage message)
        {
            try
            {
                logger.LogInformation("{Message} : Synchronized {Name}", nameof(LegacyOrderAcceptedMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }
                if (message?.Payload?.FloristIdentifier == default)
                {
                    logger.LogWarning("FloristIdentifier is null");
                }
                var executorFlorist = await iFloristsRepository.GetByIdentifier(message?.Payload?.FloristIdentifier);
                if (executorFlorist == null)
                {
                    logger.LogWarning("FloristIdentifier is unknown");
                }

                GlobalOrderAccepted globalOrderAccepted = message;

                IOrder orderBeforeUpdate = await retryHandler.Handle409Conflict(orderService.HandleOrderAccepted, globalOrderAccepted, nameof(LegacyOrderAcceptedMessage));
                if (orderBeforeUpdate != null)
                {
                    OrderNewHistoryRecordMessageBuilder builder = new OrderNewHistoryRecordMessageBuilder();
                    builder.AddCommerceToolsID(message.Payload.OrderIdentifier)
                        .AddOrderNumber(orderBeforeUpdate.OrderNumber)
                        .AddLegacyOrderNumber(orderBeforeUpdate.GetLegacyOrderNumber())
                        .AddInitialOrderStatus(orderBeforeUpdate.GetFloristOrderStatus())
                        .AddExecutingFloristId(orderBeforeUpdate.GetExecutingFloristId())
                        .AddOrderAmount(orderBeforeUpdate.GetTotalItemsPrice() + orderBeforeUpdate.GetDeliveryPrice())
                        .AddRequest(message.Serialize())
                        .AddOrderAction("ACCEPTED DONE")
                        .AddCtOrderPreUpdate(orderBeforeUpdate?.Serialize(SerializerType.CommerceTools, serializerService))
                        .AddMessage($"The order {orderBeforeUpdate.OrderNumber} has been updated as ACCEPTED by florist {globalOrderAccepted.FloristIdentier}")
                        .Build();
                    await kafkaPublisher.Publish(builder.Build(), kafkaTopicsSettings.CurrentValue.Order);

                    logger.LogInformation($"Order with id={message?.Payload?.OrderIdentifier} delivery address updated");
                }
                else
                {
                    logger.LogWarning($"Order with id={message?.Payload?.OrderIdentifier} not found");
                }

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderAcceptedMessage), message.GetMessageKey(), message.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderAcceptedMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(LegacyOrderDeliveredMessage message)
        {
            try
            {
                logger.LogInformation("{Message} : Synchronized {Name}", nameof(LegacyOrderDeliveredMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }
                if (message?.Payload?.FloristIdentifier == default)
                {
                    logger.LogWarning("FloristIdentifier is null");

                }
                var executorFlorist = await iFloristsRepository.GetByIdentifier(message?.Payload?.FloristIdentifier);
                if (executorFlorist == null)
                {
                    logger.LogWarning("FloristIdentifier is unknown");
                }

                GlobalOrderDelivered globalOrderDelivered = message;

                IOrder orderBeforeUpdate = await retryHandler.Handle409Conflict(orderService.HandleOrderDelivered, globalOrderDelivered, nameof(LegacyOrderDeliveredMessage));
                if (orderBeforeUpdate != null)
                {
                    OrderNewHistoryRecordMessageBuilder builder = new OrderNewHistoryRecordMessageBuilder();
                    builder.AddCommerceToolsID(message.Payload.OrderIdentifier)
                        .AddOrderNumber(orderBeforeUpdate.OrderNumber)
                        .AddLegacyOrderNumber(orderBeforeUpdate.GetLegacyOrderNumber())
                        .AddInitialOrderStatus(orderBeforeUpdate.GetFloristOrderStatus())
                        .AddExecutingFloristId(orderBeforeUpdate.GetExecutingFloristId())
                        .AddOrderAmount(orderBeforeUpdate.GetTotalItemsPrice() + orderBeforeUpdate.GetDeliveryPrice())
                        .AddRequest(message.Serialize())
                        .AddOrderAction("DELIVERED DONE")
                        .AddCtOrderPreUpdate(orderBeforeUpdate?.Serialize(SerializerType.CommerceTools, serializerService))
                        .AddMessage($"The order {orderBeforeUpdate.OrderNumber} has been updated as DELIVERED by florist {globalOrderDelivered.FloristIdentier}")
                        .Build();
                    await kafkaPublisher.Publish(builder.Build(), kafkaTopicsSettings.CurrentValue.Order);

                    logger.LogInformation($"Order with id={message?.Payload?.OrderIdentifier} delivery address updated");
                }
                else
                {
                    logger.LogWarning($"Order with id={message?.Payload?.OrderIdentifier} not found");
                }

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderDeliveredMessage), message.GetMessageKey(), message.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderDeliveredMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(LegacyOrderRejectedMessage message)
        {
            try
            {
                logger.LogInformation("{Message} : Synchronized {Name}", nameof(LegacyOrderRejectedMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }
                if (message?.Payload?.FloristIdentifier == default)
                {
                    logger.LogWarning("FloristIdentifier is null");
                    return;
                }
                var executorFlorist = await iFloristsRepository.GetByIdentifier(message?.Payload?.FloristIdentifier);
                if (executorFlorist == null)
                {
                    logger.LogWarning("FloristIdentifier is unknown");
                    return;
                }

                GlobalOrderRejected globalOrderRejected = message;

                IOrder orderBeforeUpdate = await retryHandler.Handle409Conflict(orderService.HandleOrderRejected, globalOrderRejected, nameof(LegacyOrderRejectedMessage));
                if (orderBeforeUpdate != null)
                {
                    OrderNewHistoryRecordMessageBuilder builder = new OrderNewHistoryRecordMessageBuilder();
                    builder.AddCommerceToolsID(message.Payload.OrderIdentifier)
                        .AddOrderNumber(orderBeforeUpdate.OrderNumber)
                        .AddLegacyOrderNumber(orderBeforeUpdate.GetLegacyOrderNumber())
                        .AddInitialOrderStatus(orderBeforeUpdate.GetFloristOrderStatus())
                        .AddExecutingFloristId(orderBeforeUpdate.GetExecutingFloristId())
                        .AddOrderAmount(orderBeforeUpdate.GetTotalItemsPrice() + orderBeforeUpdate.GetDeliveryPrice())
                        .AddRequest(message.Serialize())
                        .AddOrderAction("REFUSED DONE")
                        .AddCtOrderPreUpdate(orderBeforeUpdate?.Serialize(SerializerType.CommerceTools, serializerService))
                        .AddMessage($"The order {orderBeforeUpdate.OrderNumber} has been updated as REFUSED by florist {globalOrderRejected.FloristIdentier}")
                        .Build();
                    await kafkaPublisher.Publish(builder.Build(), kafkaTopicsSettings.CurrentValue.Order);

                    logger.LogInformation($"Order with id={message?.Payload?.OrderIdentifier} delivery address updated");
                }
                else
                {
                    logger.LogWarning($"Order with id={message?.Payload?.OrderIdentifier} not found");
                }

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderRejectedMessage), message.GetMessageKey(), message.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderRejectedMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(LegacyOrderItemUpdatedMessage message)
        {
            try
            {


                logger.LogInformation("{Message} : Synchronized {Name}", nameof(LegacyOrderItemUpdatedMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }
                if (message.Payload?.InhibOrderReactorProcess ?? false)
                {
                    logger.LogInformation("Fr case : We not process the payload because of InhibOrderReactorProcess in order to avoid duplicate updateProductFields process");
                    return;
                }
                GlobalOrderItemUpdated orderItemUpdated = message;
                if (orderItemUpdated == default)
                {
                    logger.LogWarning("The OrderItemTypeEnum field is null");
                    return;
                }

                await orderService.Initialize();

                IOrder orderBeforeUpdate = await retryHandler.Handle409Conflict(orderService.HandleOrderItemUpdated, orderItemUpdated, nameof(LegacyOrderItemUpdatedMessage));
                if (orderBeforeUpdate != null)
                {
                    OrderNewHistoryRecordMessageBuilder builder = new OrderNewHistoryRecordMessageBuilder();
                    builder.AddCommerceToolsID(message.Payload.OrderIdentifier)
                        .AddOrderNumber(orderBeforeUpdate.OrderNumber)
                        .AddLegacyOrderNumber(orderBeforeUpdate.GetLegacyOrderNumber())
                        .AddInitialOrderStatus(orderBeforeUpdate.GetFloristOrderStatus())
                        .AddExecutingFloristId(orderBeforeUpdate.GetExecutingFloristId())
                        .AddOrderAmount(orderBeforeUpdate.GetTotalItemsPrice() + orderBeforeUpdate.GetDeliveryPrice())
                        .AddRequest(message.Serialize())
                        .AddOrderAction("ITEM UPDATED DONE")
                        .AddCtOrderPreUpdate(orderBeforeUpdate?.Serialize(SerializerType.CommerceTools, serializerService))
                        .AddMessage($"The items within the order {orderBeforeUpdate.OrderNumber} have been updated")
                        .Build();
                    await kafkaPublisher.Publish(builder.Build(), kafkaTopicsSettings.CurrentValue.Order);

                    logger.LogInformation($"Order with id={message?.Payload?.OrderIdentifier} order item updated");
                }
                else
                {
                    logger.LogWarning($"Order with id={message?.Payload?.OrderIdentifier} not found");
                }

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderItemUpdatedMessage), message.GetMessageKey(), message.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderItemUpdatedMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(LegacyOrderItemExecutorAmountUpdatedMessage message)
        {
            try
            {
                logger.LogInformation("{Message} : Synchronized {Name}", nameof(LegacyOrderItemExecutorAmountUpdatedMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }
                GlobalOrderItemExecutorAmountUpdated orderItemExecutorAmountUpdated = message;
                if (orderItemExecutorAmountUpdated == default)
                {
                    logger.LogWarning("The OrderItemTypeEnum field is null");
                    return;
                }

                //await _orderService.Initialize();

                IOrder orderBeforeUpdate = await retryHandler.Handle409Conflict(orderService.HandleOrderItemExecutorAmountUpdated, orderItemExecutorAmountUpdated, nameof(LegacyOrderItemExecutorAmountUpdatedMessage));
                if (orderBeforeUpdate != null)
                {
                    OrderNewHistoryRecordMessageBuilder builder = new OrderNewHistoryRecordMessageBuilder();
                    builder.AddCommerceToolsID(message.Payload.OrderIdentifier)
                        .AddOrderNumber(orderBeforeUpdate.OrderNumber)
                        .AddLegacyOrderNumber(orderBeforeUpdate.GetLegacyOrderNumber())
                        .AddInitialOrderStatus(orderBeforeUpdate.GetFloristOrderStatus())
                        .AddExecutingFloristId(orderBeforeUpdate.GetExecutingFloristId())
                        .AddOrderAmount(orderBeforeUpdate.GetTotalItemsPrice() + orderBeforeUpdate.GetDeliveryPrice())
                        .AddRequest(message.Serialize())
                        .AddOrderAction("ITEM EXECUTOR AMOUNT UPDATED DONE")
                        .AddCtOrderPreUpdate(orderBeforeUpdate?.Serialize(SerializerType.CommerceTools, serializerService))
                        .AddMessage($"The items within the order {orderBeforeUpdate.OrderNumber} have been updated")
                        .Build();
                    await kafkaPublisher.Publish(builder.Build(), kafkaTopicsSettings.CurrentValue.Order);

                    logger.LogInformation($"Order with id={message?.Payload?.OrderIdentifier} order item executor amount updated");
                }
                else
                {
                    logger.LogWarning($"Order with id={message?.Payload?.OrderIdentifier} not found");
                }

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderItemExecutorAmountUpdatedMessage), message.GetMessageKey(), message.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderItemExecutorAmountUpdatedMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(LegacyOrderAssignationRemovedMessage message)
        {
            try
            {
                logger.LogInformation("{Message} : Synchronized {Name}", nameof(LegacyOrderAssignationRemovedMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }
                if (message?.Payload?.FloristIdentifier == default)
                {
                    logger.LogWarning("FloristIdentifier is null");
                    return;
                }
                var executorFlorist = await iFloristsRepository.GetByIdentifier(message?.Payload?.FloristIdentifier);
                if (executorFlorist == null)
                {
                    logger.LogWarning("FloristIdentifier is unknown");
                    return;
                }

                GlobalOrderAssignationRemoved globalOrderAssignationRemoved = message;

                IOrder orderBeforeUpdate = await retryHandler.Handle409Conflict(orderService.HandleOrderAssignationRemoved, globalOrderAssignationRemoved, nameof(LegacyOrderAssignationRemovedMessage));
                if (orderBeforeUpdate != null)
                {
                    OrderNewHistoryRecordMessageBuilder builder = new OrderNewHistoryRecordMessageBuilder();
                    builder.AddCommerceToolsID(message.Payload.OrderIdentifier)
                        .AddOrderNumber(orderBeforeUpdate.OrderNumber)
                        .AddLegacyOrderNumber(orderBeforeUpdate.GetLegacyOrderNumber())
                        .AddInitialOrderStatus(orderBeforeUpdate.GetFloristOrderStatus())
                        .AddExecutingFloristId(orderBeforeUpdate.GetExecutingFloristId())
                        .AddOrderAmount(orderBeforeUpdate.GetTotalItemsPrice() + orderBeforeUpdate.GetDeliveryPrice())
                        .AddRequest(message.Serialize())
                        .AddOrderAction("ASSIGNATION REMOVED DONE")
                        .AddCtOrderPreUpdate(orderBeforeUpdate?.Serialize(SerializerType.CommerceTools, serializerService))
                        .AddMessage($"The order {orderBeforeUpdate.OrderNumber} is not assigned anymore to any florist")
                        .Build();
                    await kafkaPublisher.Publish(builder.Build(), kafkaTopicsSettings.CurrentValue.Order);

                    logger.LogInformation($"Order with id={message?.Payload?.OrderIdentifier} assignation removed");
                }
                else
                {
                    logger.LogWarning($"Order with id={message?.Payload?.OrderIdentifier} not found");
                }

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderAssignationRemovedMessage), message.GetMessageKey(), message.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderAssignationRemovedMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(LegacyOrderAcceptedOnBehalfMessage message)
        {
            try
            {
                logger.LogInformation("{Message} : Synchronized {Name}", nameof(LegacyOrderAcceptedOnBehalfMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }

                GlobalOrderAcceptedOnBehalf globalOrderAccepted = message;

                IOrder orderBeforeUpdate = await retryHandler.Handle409Conflict(orderService.HandleOrderAcceptedOnBehalf, globalOrderAccepted, nameof(LegacyOrderAcceptedOnBehalfMessage));
                if (orderBeforeUpdate != null)
                {
                    OrderNewHistoryRecordMessageBuilder builder = new OrderNewHistoryRecordMessageBuilder();
                    builder.AddCommerceToolsID(message.Payload.OrderIdentifier)
                        .AddOrderNumber(orderBeforeUpdate.OrderNumber)
                        .AddLegacyOrderNumber(orderBeforeUpdate.GetLegacyOrderNumber())
                        .AddInitialOrderStatus(orderBeforeUpdate.GetFloristOrderStatus())
                        .AddExecutingFloristId(orderBeforeUpdate.GetExecutingFloristId())
                        .AddOrderAmount(orderBeforeUpdate.GetTotalItemsPrice() + orderBeforeUpdate.GetDeliveryPrice())
                        .AddRequest(message.Serialize())
                        .AddOrderAction("ACCEPTED ON BEHALF DONE")
                        .AddCtOrderPreUpdate(orderBeforeUpdate?.Serialize(SerializerType.CommerceTools, serializerService))
                        .AddMessage($"The order {orderBeforeUpdate.OrderNumber} has been updated as ACCEPTED on behalf")
                        .Build();
                    await kafkaPublisher.Publish(builder.Build(), kafkaTopicsSettings.CurrentValue.Order);

                    logger.LogInformation($"Order with id={message?.Payload?.OrderIdentifier} updated to accepted");
                }
                else
                {
                    logger.LogWarning($"Order with id={message?.Payload?.OrderIdentifier} not found");
                }

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderAcceptedOnBehalfMessage), message.GetMessageKey(), message.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderAcceptedOnBehalfMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(LegacyOrderRejectedOnBehalfMessage message)
        {
            try
            {
                logger.LogInformation("{Message} : Synchronized {Name}", nameof(LegacyOrderRejectedOnBehalfMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }

                GlobalOrderRejectedOnBehalf globalOrderRejected = message;

                IOrder orderBeforeUpdate = await retryHandler.Handle409Conflict(orderService.HandleOrderRejectedOnBehalf, globalOrderRejected, nameof(LegacyOrderRejectedOnBehalfMessage));
                if (orderBeforeUpdate != null)
                {
                    OrderNewHistoryRecordMessageBuilder builder = new OrderNewHistoryRecordMessageBuilder();
                    builder.AddCommerceToolsID(message.Payload.OrderIdentifier)
                        .AddOrderNumber(orderBeforeUpdate.OrderNumber)
                        .AddLegacyOrderNumber(orderBeforeUpdate.GetLegacyOrderNumber())
                        .AddInitialOrderStatus(orderBeforeUpdate.GetFloristOrderStatus())
                        .AddExecutingFloristId(orderBeforeUpdate.GetExecutingFloristId())
                        .AddOrderAmount(orderBeforeUpdate.GetTotalItemsPrice() + orderBeforeUpdate.GetDeliveryPrice())
                        .AddRequest(message.Serialize())
                        .AddOrderAction("REFUSED ON BEHALF DONE")
                        .AddCtOrderPreUpdate(orderBeforeUpdate?.Serialize(SerializerType.CommerceTools, serializerService))
                        .AddMessage($"The order {orderBeforeUpdate.OrderNumber} has been updated as REFUSED on behalf")
                        .Build();
                    await kafkaPublisher.Publish(builder.Build(), kafkaTopicsSettings.CurrentValue.Order);

                    logger.LogInformation($"Order with id={message?.Payload?.OrderIdentifier} updated to refused");
                }
                else
                {
                    logger.LogWarning($"Order with id={message?.Payload?.OrderIdentifier} not found");
                }

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderRejectedOnBehalfMessage), message.GetMessageKey(), message.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderRejectedOnBehalfMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(LegacyOrderSentMessage message)
        {
            try
            {
                logger.LogInformation("{Message} : Synchronized {Name}", nameof(LegacyOrderSentMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }

                GlobalOrderSent globalOrderSent = message;

                IOrder orderBeforeUpdate = await retryHandler.Handle409Conflict(orderService.HandleOrderSent, globalOrderSent, nameof(LegacyOrderSentMessage));
                if (orderBeforeUpdate != null)
                {
                    OrderNewHistoryRecordMessageBuilder builder = new OrderNewHistoryRecordMessageBuilder();
                    builder.AddCommerceToolsID(message.Payload.OrderIdentifier)
                        .AddOrderNumber(orderBeforeUpdate.OrderNumber)
                        .AddLegacyOrderNumber(orderBeforeUpdate.GetLegacyOrderNumber())
                        .AddInitialOrderStatus(orderBeforeUpdate.GetFloristOrderStatus())
                        .AddExecutingFloristId(orderBeforeUpdate.GetExecutingFloristId())
                        .AddOrderAmount(orderBeforeUpdate.GetTotalItemsPrice() + orderBeforeUpdate.GetDeliveryPrice())
                        .AddRequest(message.Serialize())
                        .AddOrderAction("SENT DONE")
                        .AddCtOrderPreUpdate(orderBeforeUpdate?.Serialize(SerializerType.CommerceTools, serializerService))
                        .AddMessage($"The order {orderBeforeUpdate.OrderNumber} has been updated as SENT")
                        .Build();
                    await kafkaPublisher.Publish(builder.Build(), kafkaTopicsSettings.CurrentValue.Order);

                    logger.LogInformation($"Order with id={message?.Payload?.OrderIdentifier} updated to accepted");
                }
                else
                {
                    logger.LogWarning($"Order with id={message?.Payload?.OrderIdentifier} not found");
                }

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderSentMessage), message.GetMessageKey(), message.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderSentMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(LegacyOrderRecipientNameUpdatedMessage message)
        {
            try
            {
                logger.LogInformation("{Message} : Synchronized {Name}", nameof(LegacyOrderRecipientNameUpdatedMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }

                GlobalOrderRecipientNameUpdated globalObject = message;

                IOrder orderBeforeUpdate = await retryHandler.Handle409Conflict(orderService.HandleOrderRecipientNameUpdated, globalObject, nameof(LegacyOrderRecipientNameUpdatedMessage));
                if (orderBeforeUpdate != null)
                {
                    OrderNewHistoryRecordMessageBuilder builder = new OrderNewHistoryRecordMessageBuilder();
                    builder.AddCommerceToolsID(message.Payload.OrderIdentifier)
                        .AddOrderNumber(orderBeforeUpdate.OrderNumber)
                        .AddLegacyOrderNumber(orderBeforeUpdate.GetLegacyOrderNumber())
                        .AddInitialOrderStatus(orderBeforeUpdate.GetFloristOrderStatus())
                        .AddExecutingFloristId(orderBeforeUpdate.GetExecutingFloristId())
                        .AddOrderAmount(orderBeforeUpdate.GetTotalItemsPrice() + orderBeforeUpdate.GetDeliveryPrice())
                        .AddRequest(message.Serialize())
                        .AddOrderAction("RECIPIENT NAME UPDATED DONE")
                        .AddCtOrderPreUpdate(orderBeforeUpdate?.Serialize(SerializerType.CommerceTools, serializerService))
                        .AddMessage($"The recipient name for order {orderBeforeUpdate.OrderNumber} has been updated as SENT")
                        .Build();
                    await kafkaPublisher.Publish(builder.Build(), kafkaTopicsSettings.CurrentValue.Order);

                    logger.LogInformation($"Recipient name for order with id={message?.Payload?.OrderIdentifier} updated");
                }
                else
                {
                    logger.LogWarning($"Order with id={message?.Payload?.OrderIdentifier} not found");
                }

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderRecipientNameUpdatedMessage), message.GetMessageKey(), message.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderRecipientNameUpdatedMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(LegacyOrderRecipientPhoneNumberUpdatedMessage message)
        {
            try
            {
                logger.LogInformation("{Message} : Synchronized {Name}", nameof(LegacyOrderRecipientPhoneNumberUpdatedMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }

                GlobalOrderRecipientPhoneNumberUpdated globalOrderSent = message;

                IOrder orderBeforeUpdate = await retryHandler.Handle409Conflict(orderService.HandleOrderRecipientPhoneNumberUpdated, globalOrderSent, nameof(LegacyOrderRecipientPhoneNumberUpdatedMessage));
                if (orderBeforeUpdate != null)
                {
                    OrderNewHistoryRecordMessageBuilder builder = new OrderNewHistoryRecordMessageBuilder();
                    builder.AddCommerceToolsID(message.Payload.OrderIdentifier)
                        .AddOrderNumber(orderBeforeUpdate.OrderNumber)
                        .AddLegacyOrderNumber(orderBeforeUpdate.GetLegacyOrderNumber())
                        .AddInitialOrderStatus(orderBeforeUpdate.GetFloristOrderStatus())
                        .AddExecutingFloristId(orderBeforeUpdate.GetExecutingFloristId())
                        .AddOrderAmount(orderBeforeUpdate.GetTotalItemsPrice() + orderBeforeUpdate.GetDeliveryPrice())
                        .AddRequest(message.Serialize())
                        .AddOrderAction("RECIPIENT PHONE NUMBER UPDATED DONE")
                        .AddCtOrderPreUpdate(orderBeforeUpdate?.Serialize(SerializerType.CommerceTools, serializerService))
                        .AddMessage($"The recipient phone number for order {orderBeforeUpdate.OrderNumber} has been updated as SENT")
                        .Build();
                    await kafkaPublisher.Publish(builder.Build(), kafkaTopicsSettings.CurrentValue.Order);

                    logger.LogInformation($"Recipient phone number for order with id={message?.Payload?.OrderIdentifier} updated");
                }
                else
                {
                    logger.LogWarning($"Order with id={message?.Payload?.OrderIdentifier} not found");
                }

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderRecipientPhoneNumberUpdatedMessage), message.GetMessageKey(), message.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderRecipientPhoneNumberUpdatedMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(LegacyOrderRecipientCoordinatesUpdatedMessage message)
        {
            try
            {
                logger.LogInformation("{Message} : Synchronized {Name}", nameof(LegacyOrderRecipientCoordinatesUpdatedMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }

                GlobalOrderRecipientCoordinatesUpdated globalOrderSent = message;

                IOrder orderBeforeUpdate = await retryHandler.Handle409Conflict(orderService.HandleOrderRecipientCoordinatesUpdated, globalOrderSent, nameof(LegacyOrderRecipientCoordinatesUpdatedMessage));
                if (orderBeforeUpdate != null)
                {
                    OrderNewHistoryRecordMessageBuilder builder = new OrderNewHistoryRecordMessageBuilder();
                    builder.AddCommerceToolsID(message.Payload.OrderIdentifier)
                        .AddOrderNumber(orderBeforeUpdate.OrderNumber)
                        .AddLegacyOrderNumber(orderBeforeUpdate.GetLegacyOrderNumber())
                        .AddInitialOrderStatus(orderBeforeUpdate.GetFloristOrderStatus())
                        .AddExecutingFloristId(orderBeforeUpdate.GetExecutingFloristId())
                        .AddOrderAmount(orderBeforeUpdate.GetTotalItemsPrice() + orderBeforeUpdate.GetDeliveryPrice())
                        .AddRequest(message.Serialize())
                        .AddOrderAction("COORDINATES UPDATED DONE")
                        .AddCtOrderPreUpdate(orderBeforeUpdate?.Serialize(SerializerType.CommerceTools, serializerService))
                        .AddMessage($"The coordinates for order {orderBeforeUpdate.OrderNumber} has been updated as SENT")
                        .Build();
                    await kafkaPublisher.Publish(builder.Build(), kafkaTopicsSettings.CurrentValue.Order);

                    logger.LogInformation($"Coordinates for order with id={message?.Payload?.OrderIdentifier} updated");
                }
                else
                {
                    logger.LogWarning($"Order with id={message?.Payload?.OrderIdentifier} not found");
                }

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(LegacyOrderRecipientCoordinatesUpdatedMessage), message.GetMessageKey(), message.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(LegacyOrderRecipientCoordinatesUpdatedMessage)} for order {message.GetMessageKey()}. Payload : {message.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        #region RAO France Legacy Messages

        public async Task SychronizeProcess(OrderPlacedMessage message)
        {
            try
            {
                if (message.Payload is null)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }

                logger.LogInformation("Process Order id {id} , on event type {type}", message.Payload?.OrderId, nameof(OrderPlacedMessage));

                if (message?.Payload?.Status?.Status == "NPA")
                {
                    logger.LogInformation("Process Order id {id} , on event type {type} skipped because of NPA Status", message.Payload?.OrderId, nameof(OrderPlacedMessage));
                    return;
                }

                var order = await orderService.GetByOrderNumber(message?.Payload?.OrderId); // the orderId in the message is the OrderId ITXXXXX into the RAO that match with the CT orderNumber not the CT ID

                if (order != null)
                {
                    logger.LogWarning("OrderId {OrderId} already exists in CommerceTools , update process", message.Payload.OrderId);

                    var orderUpdatedMessage = await orderService.BuildOrderMessageFromRaoOrderMessage<OrderUpdatedMessage, OrderUpdatedPayload, OrderPlacedMessage, OrderPlacedPayload>(message);
                    await UpdateOrderProcess(orderUpdatedMessage, order);
                }
                else
                {
                    var legacyOrderCreatedMessage = await orderService.BuildOrderMessageFromRaoOrderMessage<LegacyOrderCreatedMessage, LegacyOrderCreatedPayload, OrderPlacedMessage, OrderPlacedPayload>(message);
                    if (legacyOrderCreatedMessage?.Payload?.Products.Count == 0)
                        throw new Exception(string.Format("GetEligibleProducts returns no product by checking following RAO products : {0}", legacyOrderCreatedMessage?.Payload?.Products?.Serialize()));
                    await CreateOrderProcess(legacyOrderCreatedMessage);
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(OrderPlacedMessage), message?.Payload?.OrderId, message?.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(OrderPlacedMessage)} for order {message?.Payload?.OrderId}. Payload : {message?.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(OrderAssignmentMessage message)
        {
            try
            {
                if (message.Payload is null)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }

                logger.LogInformation("Process Order id {id} , on event type {type}", message.Payload?.OrderId, nameof(OrderAssignmentMessage));

                if (message?.Payload?.Status?.Status == "NPA")
                {
                    logger.LogInformation("Process Order id {id} , on event type {type} skipped because of NPA Status", message.Payload?.OrderId, nameof(OrderUpdatedMessage));
                    return;
                }

                var order = await orderService.GetByOrderNumber(message?.Payload?.OrderId); // the orderId in the message is the OrderId ITXXXXX into the RAO that match with the CT orderNumber not the CT ID


                if (order != null)
                {

                    logger.LogWarning("OrderId {OrderId} already exists in CommerceTools , update process", message.Payload.OrderId);

                    if (message.Payload.Emission.AssignmentState.Equals("UNASSIGNABLE", StringComparison.OrdinalIgnoreCase))
                    {
                        var orderStatusUpdate = orderService.CreateOrderUpdate(order, [new KeyValuePair<OrderDifference, object>(OrderDifference.Status, StatusEnum.ASSIGNATION_NOT_POSSIBLE.ToString())]);
                        await orderService.PostOrderUpdateWithRetry(orderStatusUpdate, order.Id, order.OrderNumber);
                        return;
                    }

                    var orderUpdatedMessage = await orderService.BuildOrderMessageFromRaoOrderMessage<OrderUpdatedMessage, OrderUpdatedPayload, OrderAssignmentMessage, OrderAssignmentPayload>(message);
                    await UpdateOrderProcess(orderUpdatedMessage, order);

                }
                else
                {
                    if (message.Payload.Emission.AssignmentState.Equals("UNASSIGNABLE", StringComparison.OrdinalIgnoreCase))
                    {
                        logger.LogError("Can't process UNASSIGNABLE OrderId {OrderId} because products are empty so we can't perform the creation process to CommerceTools", message.Payload.OrderId);
                    }
                    else
                    {
                        var legacyOrderCreatedMessage = await orderService.BuildOrderMessageFromRaoOrderMessage<LegacyOrderCreatedMessage, LegacyOrderCreatedPayload, OrderAssignmentMessage, OrderAssignmentPayload>(message);
                        if (legacyOrderCreatedMessage?.Payload?.Products.Count == 0)
                            throw new Exception(string.Format("GetEligibleProducts returns no product by checking following RAO products : {0}", legacyOrderCreatedMessage?.Payload?.Products?.Serialize()));
                        order = await CreateOrderProcess(legacyOrderCreatedMessage);
                    }
                }

                if (order != null)
                {
                    if (message.Payload.Emission.AssignmentState.Equals("ASSIGNED", StringComparison.OrdinalIgnoreCase))
                    {
                        var legacyOrderAssignedMessage = await orderService.BuildOrderMessageFromRaoOrderMessage<LegacyOrderAssignedMessage, LegacyOrderAssignedPayload, OrderAssignmentMessage, OrderAssignmentPayload>(message, order.Id);
                        await kafkaPublisher.Publish<LegacyOrderAssignedMessage>(legacyOrderAssignedMessage, kafkaTopicsSettings.CurrentValue.Order, message.Payload.OrderId);
                    }
                    else await (message.Payload.Emission.AssignmentState switch
                    {
                        "REJECTED" => kafkaPublisher.Publish<LegacyOrderAssignationRemovedMessage>((LegacyOrderAssignationRemovedMessage)(order.Id, message), kafkaTopicsSettings.CurrentValue.Order, message.Payload.OrderId),
                        "CANCELED" => kafkaPublisher.Publish<LegacyOrderCancelledMessage>((LegacyOrderCancelledMessage)(order.Id, message), kafkaTopicsSettings.CurrentValue.Order, message.Payload.OrderId),
                        _ => Task.CompletedTask
                    });
                }
                else
                    logger.LogWarning("AssignProcess skipped for order {OrderId} because order is null", message.Payload.OrderId);

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(OrderAssignmentMessage), message?.Payload?.OrderId, message?.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(OrderAssignmentMessage)} for order {message?.Payload?.OrderId}. Payload : {message?.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(OrderUpdatedMessage message)
        {
            try
            {
                if (message.Payload is null)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }

                logger.LogInformation("Process Order id {id} , on event type {type}", message.Payload?.OrderId, nameof(OrderUpdatedMessage));

                if (message?.Payload?.Status?.Status == "NPA")
                {
                    logger.LogInformation("Process Order id {id} , on event type {type} skipped because of NPA Status", message.Payload?.OrderId, nameof(OrderUpdatedMessage));
                    return;
                }

                var order = await orderService.GetByOrderNumber(message.Payload?.OrderId!); // the orderId in the message is the OrderId ITXXXXX into the RAO that match with the CT orderNumber not the CT ID
                if (order == null)
                {
                    logger.LogWarning("Order with id {OrderIdentifier} NOT found, try to create", message?.Payload?.OrderId);
                    var legacyOrderCreatedMessage = await orderService.BuildOrderMessageFromRaoOrderMessage<LegacyOrderCreatedMessage, LegacyOrderCreatedPayload, OrderUpdatedMessage, OrderUpdatedPayload>(message);
                    if (legacyOrderCreatedMessage?.Payload?.Products.Count == 0)
                        throw new Exception(string.Format("GetEligibleProducts returns no product by checking following RAO products : {0}", legacyOrderCreatedMessage?.Payload?.Products?.Serialize()));
                    await CreateOrderProcess(legacyOrderCreatedMessage!);
                }
                else
                    await UpdateOrderProcess(message, order);


            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(OrderUpdatedMessage), message?.Payload?.OrderId, message?.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(OrderUpdatedMessage)} for order {message?.Payload?.OrderId}. Payload : {message?.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(OrderManagementStatusMessage message)
        {
            try
            {
                if (message.Payload is null)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }

                logger.LogInformation("Process Order id {id} , on event type {type}", message.Payload?.OrderId, nameof(OrderManagementStatusMessage));


                await orderService.HandleRAOLegacyOrderManagementStatus(message);

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(OrderManagementStatusMessage), message?.Payload?.OrderId, message?.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(OrderManagementStatusMessage)} for order {message?.Payload?.OrderId}. Payload : {message?.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);

            }
        }

        public async Task SychronizeProcess(InvoiceMessage message)
        {
            try
            {
                if (message?.Payload is null)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }

                logger.LogInformation("Process Order id {id} , on event type {type} for floristId {floristId}", message.Payload.OrderId, nameof(InvoiceMessage), message.Payload.FloristId);

                var order = await orderService.GetByOrderNumber(message.Payload.OrderId); // the orderId in the message is the OrderId ITXXXXX into the RAO that match with the CT orderNumber not the CT ID
                if (order == null)
                {
                    logger.LogError("Order with id {OrderIdentifier} NOT found in CT, we cant save the Florist order invoice : florist id : {florist}", message.Payload.OrderId, message.Payload.FloristId);
                    await (slackAlertService?.SendErrorAlertAsync($"Order with id {message.Payload.OrderId} NOT found in CT, we cant save the Florist order invoice : florist id : {message.Payload.FloristId} , full message : {message.Serialize()}") ?? Task.CompletedTask);
                }
                else
                {
                    // handle edge case where the floristId in the invoiceMessage do not match with the executing floristId in the order (wrong sequence invoice received)
                    var fieldComparer = new FieldComparer();
                    if (!fieldComparer.Equals(message.Payload.FloristId, order.GetExecutingFloristId()))
                    {
                        logger.LogWarning("Order with id {OrderIdentifier} has executing florist id {executingFloristId} but invoice message has florist id {floristId}, we will not update the order with the invoice url / full message {mess}", message.Payload.OrderId, order.GetExecutingFloristId(), message.Payload.FloristId , message.Serialize());
                        return;
                    }

                    var actions = new List<IOrderUpdateAction>
                    {
                        new OrderSetCustomFieldAction
                        {
                            Name = CtOrderCustomAttributesNames.Order.EXECUTING_FLORIST_INVOICE_URL,
                            Value = message.Payload.Url
                        }
                    };

                    await orderService.PostOrderUpdateWithRetry(new OrderUpdate { Version = order.Version, Actions = actions }, order.Id, order.OrderNumber);

                    logger.LogInformation("Order with id {OrderIdentifier} updated in CT with invoice url {url} for floristId {floristId}", message.Payload.OrderId, message.Payload.Url, message.Payload.FloristId);
                }



            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {ex}", nameof(SychronizeProcess), nameof(InvoiceMessage), message?.Payload?.OrderId, message?.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(InvoiceMessage)} for order {message?.Payload?.OrderId}. Payload : {message?.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(OrderDeliveryCourierUpdatedMessage message)
        {
            try
            {
                if (message?.Payload is null)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }

                logger.LogInformation("Process Order id {id} , on event type {type}", message.Payload?.OrderId, nameof(OrderDeliveryCourierUpdatedMessage));

                var order = await orderService.GetByOrderNumber(message?.Payload?.OrderId); // the orderId in the message is the OrderId ITXXXXX into the RAO that match with the CT orderNumber not the CT ID
                if (order == null)
                {
                    logger.LogWarning("Order with id {OrderIdentifier} NOT found", message?.Payload?.OrderId);
                    return;
                }

                string strEnumType = message.Payload.DeliveryCourierStatus.ToUpper() switch
                {
                    "BOOKED" => DeliveryStatusEnum.BOOKED.ToString(),
                    "CANCELED" => DeliveryStatusEnum.PENDING.ToString(),
                    "ADDED" => DeliveryStatusEnum.SCHEDULED.ToString(),
                    "GOING_TO_DRIVE" => DeliveryStatusEnum.PICKING.ToString(),
                    "SHOW_REFERENCE" => DeliveryStatusEnum.ALMOST_PICKING.ToString(),
                    "ARRIVED_AT_DRIVE" => DeliveryStatusEnum.WAITING_AT_PICKUP.ToString(),
                    "GOING_TO_CLIENT" => DeliveryStatusEnum.DELIVERING.ToString(),
                    "ARRIVED_AT_CLIENT" => DeliveryStatusEnum.ALMOST_DELIVERING.ToString(),
                    "VALIDATED" => DeliveryStatusEnum.DELIVERED.ToString(),
                    "REMOVED" => DeliveryStatusEnum.NO_COURIER_AVAILABLE.ToString(),
                    "DELIVERY_INCIDENT" => DeliveryStatusEnum.CANCELLED.ToString(),
                    _ => message.Payload.DeliveryCourierStatus  // Default case
                };

                if (Enum.TryParse(strEnumType.ToUpper(), out DeliveryStatusEnum deliveryStatusEnum))
                {

                    var currentProvider = order.GetDeliveryService();
                    var currentStatus = order.GetDeliveryStatus();

                    // if the message is a STUART update message and the status is CANCELLED is CT already we just skip the UpdateStatus process because of the "go to shop" status that stuart send back
                    if (!string.IsNullOrWhiteSpace(currentProvider) && currentProvider?.ToUpper() == "STUART" && !string.IsNullOrWhiteSpace(currentStatus) && currentStatus?.ToUpper() == "CANCELLED")
                    {
                        logger.LogInformation("Order with id {OrderIdentifier} already has status CANCELLED in CT for STUART provider, skipping update status process", message?.Payload?.OrderId);
                        await SychronizeProcess((LegacyOrderDeliveryCostUpdatedMessage)(order.Id, message!));
                        return;
                    }

                    await SychronizeProcess((LegacyOrderDeliveryStatusUpdatedMessage)(order.Id, message, deliveryStatusEnum));
                    await SychronizeProcess((LegacyOrderDeliveryCostUpdatedMessage)(order.Id, message));
                }
                else
                    logger.LogError("Failed to process {process} on message {statusMessage} and {costMmessage} for order {orderIdentifier}. Payload : {payload}. Reason: unable to find mapping match for status : {status}", nameof(SychronizeProcess), nameof(LegacyOrderDeliveryStatusUpdatedMessage), nameof(LegacyOrderDeliveryCostUpdatedMessage), message?.Payload?.OrderId, message?.Serialize(), message.Payload.DeliveryCourierStatus);



            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(OrderDeliveryCourierUpdatedMessage), message?.Payload?.OrderId, message?.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(OrderDeliveryCourierUpdatedMessage)} for order {message?.Payload?.OrderId}. Payload : {message?.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(OrderDeliveryCourierResetedMessage message)
        {
            try
            {
                if (message.Payload is null)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }

                logger.LogInformation("Process Order id {id} , on event type {type}", message.Payload?.OrderId, nameof(OrderDeliveryCourierResetedMessage));



                var order = await orderService.GetByOrderNumber(message?.Payload?.OrderId); // the orderId in the message is the OrderId ITXXXXX into the RAO that match with the CT orderNumber not the CT ID
                if (order == null)
                {
                    logger.LogWarning("Order with id {OrderIdentifier} NOT found", message?.Payload?.OrderId);
                    return;
                }

                await SychronizeProcess((LegacyOrderDeliveryStatusUpdatedMessage)(order.Id, message));

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(OrderDeliveryCourierResetedMessage), message?.Payload?.OrderId, message?.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(OrderDeliveryCourierResetedMessage)} for order {message?.Payload?.OrderId}. Payload : {message?.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(OrderDeliveryCourierInitializedMessage message)
        {
            try
            {
                if (message.Payload is null)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }

                logger.LogInformation("Process Order id {id} , on event type {type}", message.Payload?.OrderId, nameof(OrderDeliveryCourierInitializedMessage));



                var order = await orderService.GetByOrderNumber(message?.Payload?.OrderId); // the orderId in the message is the OrderId ITXXXXX into the RAO that match with the CT orderNumber not the CT ID
                if (order == null)
                {
                    logger.LogError("At this step, order must be exists in CommerceTools, but order with id {OrderIdentifier} NOT found", message?.Payload?.OrderId);
                    return;
                }

                await SychronizeProcess((LegacyOrderDeliveryStatusUpdatedMessage)(order.Id, message));

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(OrderDeliveryCourierInitializedMessage), message?.Payload?.OrderId, message?.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(OrderDeliveryCourierInitializedMessage)} for order {message?.Payload?.OrderId}. Payload : {message?.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        #endregion

        private async Task UpdateFloristCounterPerDay(GlobalFloristOrderPerDayModel globalFloristOrderPerDay)
        {
            // Use upsert to handle race conditions - either insert or update atomically
            await iFloristOrderPerDayRepository.ReplaceOneAsync(globalFloristOrderPerDay, insert: true);
        }

        private async Task<GetFloristCounterPerDayResult> GetFloristCounterPerDay(LegacyOrderAssignedPayload legacyOrderAssigned)
        {
            GetFloristCounterPerDayResult result = new GetFloristCounterPerDayResult { Counter = -1, OrderDeliveryDate = DateTime.MinValue };
            IOrder order = null;
            if (legacyOrderAssigned != null && !String.IsNullOrWhiteSpace(legacyOrderAssigned.OrderIdentifier) && !String.IsNullOrWhiteSpace(legacyOrderAssigned.FloristIdentifier))
            {
                order = await orderService.GetById(legacyOrderAssigned.OrderIdentifier);
                if (order != null)
                {
                    DateTime deliveryDate = DateTime.MinValue;
                    string strDeliveryDate = null;

                    if (order?.ShippingAddress?.Custom?.Fields?.Keys?.Contains(CtOrderCustomAttributesNames.ShippingAddress.DATE) ?? false)
                        strDeliveryDate = order.ShippingAddress.Custom.Fields[CtOrderCustomAttributesNames.ShippingAddress.DATE].ToString();

                    // evaluate next internal order id
                    if (DateTime.TryParseExact(strDeliveryDate, "yyyy-MM-dd", System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.None, out deliveryDate))
                    {
                        result.Counter = await iFloristOrderPerDayRepository.GetCounter(legacyOrderAssigned.FloristIdentifier, deliveryDate);
                        result.OrderDeliveryDate = deliveryDate;
                    }
                    else
                    {
                        logger.LogWarning($"The internalOrderId cannot be evaluated for order with id={legacyOrderAssigned.OrderIdentifier} and florist with id={legacyOrderAssigned.FloristIdentifier} because the custom fields date is empty [order.shippingAddress.custom.fields.date] ");
                    }
                }
            }
            return result;
        }
        private async Task<IOrder> CreateOrderProcess(LegacyOrderCreatedMessage message)
        {
            await orderService.Initialize();
            if (String.IsNullOrWhiteSpace(message?.Payload?.OrderNumber))
            {
                long nextOrderNumber = await sequenceGeneratorService.GetNext();
                if (nextOrderNumber > 0)
                {
                    message.Payload.OrderNumber = nextOrderNumber.ToString();
                }
            }

            GlobalOrderModel globalOrderModel = message;
            IOrder order = await orderService.HandleOrderCreated(globalOrderModel);
            if (order == null)
            {
                logger.LogWarning("The message with id {id} failed in the process {process} on message {message} for order {Id}. Payload : {payload}", message.MessageId, nameof(SychronizeProcess), nameof(LegacyOrderCreatedMessage), message.GetMessageKey(), message.Serialize());
            }
            else
            {

                string[] countryCodesAddDocSender = new[] { "IT2" };
                if (countryCodesAddDocSender.Contains(orderReactorSettings?.CurrentValue?.CountryCode))
                {
                    GlobalFloristModel transmittorFlorist = null;
                    if (!string.IsNullOrWhiteSpace(message?.Payload?.SenderFloristIdentifier))
                    {
                        transmittorFlorist = await iFloristsRepository.GetByIdentifier(message?.Payload?.SenderFloristIdentifier);
                        if (transmittorFlorist == null)
                        {
                            logger.LogWarning("{Message} : SenderFloristIdentifier {Florist} is unknown, OrderNumber : {OrderNumber}", nameof(LegacyOrderCreatedMessage), message.Payload.SenderFloristIdentifier, message.Payload.LegacyOrderNumber);
                        }
                        else
                        {
                            string url = orderReactorSettings?.CurrentValue?.PFsGetOrderDocumentOrderUrlFormat
                            .Replace("{orderIdentifier}", order.Id)
                            .Replace("{floristIdentifier}", transmittorFlorist.Id)
                            .Replace("{type}", ((int)ITF.SharedModels.Group.Enums.DocTypeEnum.IT_DOC_ORDER_SENDER).ToString());

                            transmittorFlorist.Documents.Add(new Document
                            {
                                CTOrderId = order.Id,
                                DocType = ITF.SharedModels.Group.Enums.DocTypeEnum.IT_DOC_ORDER_SENDER,
                                FileExtension = "pdf",
                                FileName = order.OrderNumber + ".pdf",
                                OctopusOrderId = order.OrderNumber,
                                OrderReference = order.Id,
                                Month = order.CreatedAt.Month,
                                Year = order.CreatedAt.Year,
                                Url = url
                            });

                            await iFloristsRepository.SaveDocuments(transmittorFlorist);
                        }
                    }


                }

                OrderNewHistoryRecordMessageBuilder historyBuilder = new OrderNewHistoryRecordMessageBuilder();
                historyBuilder.AddCommerceToolsID(order.Id)
                    .AddOrderNumber(order.OrderNumber)
                    .AddInitialOrderStatus(order.GetFloristOrderStatus())
                    .AddExecutingFloristId(order.GetExecutingFloristId())
                    .AddOrderAmount(order.GetTotalItemsPrice() + order.GetDeliveryPrice())
                    .AddRequest(Newtonsoft.Json.JsonConvert.SerializeObject(globalOrderModel))
                    .AddOrderAction("ORDER NUMBER GENERATED")
                    .AddMessage($"Created order in CT for legacy order {order.GetLegacyOrderNumber()} with OrderNumber {order.OrderNumber} and CommerceToolsOrderID {order.Id}")
                    .AddCtOrderPreUpdate(order?.Serialize(SerializerType.CommerceTools, serializerService));

                await kafkaPublisher.Publish(historyBuilder.Build(), kafkaTopicsSettings.CurrentValue.Order);
            }
            return order;
        }

        private async Task UpdateOrderProcess(OrderUpdatedMessage message, IOrder order)
        {

            logger.LogInformation("Starting order update process for OrderId: {OrderId}", message.Payload?.OrderId);

            var fieldsUpdated = await orderService.GetDifferencesForUpdate(message.Payload, order);

            // Log summary of differences found
            logger.LogInformation("Found {DifferencesCount} differences to update for order {OrderId}. Differences: {DifferenceKeys}",
                fieldsUpdated.Count,
                message.Payload?.OrderId,
                string.Join(", ", fieldsUpdated.Select(f => f.Key.ToString())));

            // Log detailed information about each difference
            foreach (var field in fieldsUpdated)
            {
                LogFieldDifference(field, order, message.Payload);
            }

            foreach (var field in fieldsUpdated)
            {
                logger.LogInformation("Processing field update: {FieldKey} for order {OrderId}", field.Key, message.Payload?.OrderId);
                try
                {
                    await (field.Key switch
                    {
                        OrderDifference.DeliveryMomentTime => kafkaPublisher.Publish<LegacyOrderDeliveryTimeUpdatedMessage>((LegacyOrderDeliveryTimeUpdatedMessage)(order.Id, message, (Dictionary<string, string>)field.Value), kafkaTopicsSettings.CurrentValue.Order, message.Payload.OrderId),
                        OrderDifference.DeliveryDate => kafkaPublisher.Publish<LegacyOrderDeliveryDateUpdatedMessage>((LegacyOrderDeliveryDateUpdatedMessage)(order.Id, message), kafkaTopicsSettings.CurrentValue.Order, message.Payload.OrderId),
                        OrderDifference.RecipientCoordinates => kafkaPublisher.Publish<LegacyOrderRecipientCoordinatesUpdatedMessage>((LegacyOrderRecipientCoordinatesUpdatedMessage)(order.Id, message), kafkaTopicsSettings.CurrentValue.Order, message.Payload.OrderId),
                        OrderDifference.DeliveryAddress => kafkaPublisher.Publish<LegacyOrderDeliveryAddressUpdatedMessage>((LegacyOrderDeliveryAddressUpdatedMessage)(order.Id, message), kafkaTopicsSettings.CurrentValue.Order, message.Payload.OrderId),
                        OrderDifference.DeliveryInstructions => kafkaPublisher.Publish<LegacyOrderNotesUpdatedMessage>((LegacyOrderNotesUpdatedMessage)(order.Id, message), kafkaTopicsSettings.CurrentValue.Order, message.Payload.OrderId),
                        OrderDifference.Message => kafkaPublisher.Publish<LegacyOrderCardMessageUpdatedMessage>((LegacyOrderCardMessageUpdatedMessage)(order.Id, message), kafkaTopicsSettings.CurrentValue.Order, message.Payload.OrderId),
                        OrderDifference.RecipientPhoneNumber => kafkaPublisher.Publish<LegacyOrderRecipientPhoneNumberUpdatedMessage>((LegacyOrderRecipientPhoneNumberUpdatedMessage)(order.Id, message), kafkaTopicsSettings.CurrentValue.Order, message.Payload.OrderId),
                        OrderDifference.RecipientFullName => kafkaPublisher.Publish<LegacyOrderRecipientNameUpdatedMessage>((LegacyOrderRecipientNameUpdatedMessage)(order.Id, message), kafkaTopicsSettings.CurrentValue.Order, message.Payload.OrderId),
                        OrderDifference.UpdateProduct => kafkaPublisher.Publish<LegacyOrderItemUpdatedMessage>((LegacyOrderItemUpdatedMessage)(order.Id, message, (ProductInformations)field.Value, OrderItemTypeEnum.UPDATE, true), kafkaTopicsSettings.CurrentValue.Order, message.Payload.OrderId),
                        _ => Task.CompletedTask
                    });
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Failed to process field update: {FieldKey} for order {OrderId}", field.Key, message.Payload?.OrderId);
                    throw;
                }

                //if (field.Key.Equals(OrderDifference.UpdateCTFieldsProductList))
                //{
                //    logger.LogInformation("Updating CT Product List fields for order {OrderId} with {ActionsCount} actions",
                //        order.Id, ((List<IOrderUpdateAction>)field.Value).Count);

                //    var orderBeforeUpdate = order;
                //    order = await retryHandler.Handle409Conflict(orderService.HandleOrderCTFieldsUpdated, (order.Id, (List<IOrderUpdateAction>)field.Value), nameof(orderService.HandleOrderCTFieldsUpdated));

                //    logger.LogInformation("CT fields updated successfully for order {OrderId}. Version changed from {OldVersion} to {NewVersion}",
                //        order.Id, orderBeforeUpdate.Version, order.Version);
                //}
                if (field.Key.Equals(OrderDifference.UpdateCTLineItems))
                {
                    logger.LogInformation("Updating CT line items for order {OrderId} with {ActionsCount} actions",
                        order.Id, ((List<IOrderEditUpdateAction>)field.Value).Count);

                    var orderBeforeUpdate = order;
                    order = await retryHandler.Handle409Conflict(orderService.HandleOrderLineItemsUpdated, (order.Id, (List<IOrderEditUpdateAction>)field.Value), nameof(orderService.HandleOrderLineItemsUpdated));

                    logger.LogInformation("CT line items updated successfully for order {OrderId}. Version changed from {OldVersion} to {NewVersion}",
                        order.Id, orderBeforeUpdate.Version, order.Version);

                    List<LineItemPrice> mongoLineItemsPrice = [];
                    var orderProducts = message?.Payload?.Products?.GetEligibleProducts().GroupByBundleProducts();
                    orderProducts?.FindAll(p => p.IsBundlePart() && !p.IsDiscountLineItem()).ForEach(p => mongoLineItemsPrice.Add(new LineItemPrice(p.GetProductKey(), p.GetVariantKey(), Convert.ToDecimal(p.FloristFee > 0 ? p.FloristFee : p.Privex))));
                    if (mongoLineItemsPrice.Any())
                    {
                        var orderLineItemsPrices = new OrderLineItemsPrices(order.Id, mongoLineItemsPrice);
                        await (_orderLineItemsPricesRepository?.ReplaceOneAsync(orderLineItemsPrices) ?? Task.CompletedTask);

                    }

                }
            }

            var statusAction = message.Payload?.Status?.Status switch
            {
                "LFM" => "LegacyOrderDeliveredMessage",
                "LFA" => "LegacyOrderDeliveredMessage",
                "AFF" when string.IsNullOrEmpty(message.Payload.InternationalOrderId) &&
                           !string.Equals(message.Payload.Recipient.CountryCode, "FR", StringComparison.OrdinalIgnoreCase) => "LegacyOrderSentMessage",
                _ => null
            };

            if (statusAction != null)
            {
                logger.LogInformation("Processing status synchronization: {StatusAction} for order {OrderId} with status {Status}",
                    statusAction, message.Payload?.OrderId, message.Payload?.Status?.Status);
            }

            await (message.Payload?.Status?.Status switch
            {
                "LFM" => SychronizeProcess((LegacyOrderDeliveredMessage)(order.Id, message)),
                "LFA" => SychronizeProcess((LegacyOrderDeliveredMessage)(order.Id, message)),
                "AFF" => (string.IsNullOrEmpty(message.Payload.InternationalOrderId) && !string.Equals(message.Payload.Recipient.CountryCode, "FR", StringComparison.OrdinalIgnoreCase)) ? SychronizeProcess((LegacyOrderSentMessage)(order.Id, message)) : Task.CompletedTask,
                _ => Task.CompletedTask
            });

            if (fieldsUpdated.Count > 0)
            {
                logger.LogInformation("Executing HandleRAOLegacyOrderUpdate for order {OrderId} with {UpdatedFieldsCount} fields",
                    message.Payload?.OrderId, fieldsUpdated.Count);

                var res = await orderService.HandleRAOLegacyOrderUpdate(message.Payload, order, fieldsUpdated);

                if (res.IsFailure)
                {
                    logger.LogError("Error in HandleRAOLegacyOrderUpdate process. Failed to update orderNumber {OrderNumber} from message {Message} with CT update payload: {FieldsUpdated}. Reason: {Reason}",
                        message.Payload?.OrderId,
                        message.Serialize(),
                        fieldsUpdated.Serialize(),
                        res.Error);
                }
                else
                {
                    logger.LogInformation("HandleRAOLegacyOrderUpdate completed successfully for order {OrderId}",
                        message.Payload?.OrderId);
                }
            }
            else
            {
                logger.LogInformation("No fields to update for order {OrderId}", message.Payload?.OrderId);
            }


        }

        private void LogFieldDifference(KeyValuePair<OrderDifference, object> field, IOrder order, dynamic payload)
        {
            try
            {
                var fieldKey = field.Key.ToString();
                var newValue = field.Value;

                // Get current value from order based on field type
                var currentValue = GetCurrentFieldValue(field.Key, order);

                // Log the difference with before/after values
                logger.LogInformation("Field Difference - {FieldKey}: Current='{CurrentValue}' -> New='{NewValue}' for order {OrderId}",
                    fieldKey,
                    currentValue ?? "NULL",
                    SerializeValue(newValue),
                    (string)payload?.OrderId);

                // Log additional details for complex objects
                if (newValue is Dictionary<string, object> dict)
                {
                    logger.LogInformation("Complex field {FieldKey} details: {Details}",
                        fieldKey,
                        string.Join(", ", dict.Select(kvp => $"{kvp.Key}='{kvp.Value}'")));
                }
                else if (newValue is List<IOrderUpdateAction> actions)
                {
                    logger.LogInformation("Update actions for {FieldKey}: {ActionCount} actions - {ActionTypes}",
                        fieldKey,
                        actions.Count,
                        string.Join(", ", actions.Select(a => a?.Serialize(SerializerType.CommerceTools, serializerService))));
                }
                else if (newValue is List<IOrderEditUpdateAction> editActions)
                {
                    logger.LogInformation("Edit actions for {FieldKey}: {ActionCount} actions - {ActionTypes}",
                        fieldKey,
                        editActions.Count,
                        string.Join(", ", editActions.Select(a => a?.Serialize(SerializerType.CommerceTools, serializerService))));
                }
            }
            catch (Exception ex)
            {
                logger.LogWarning(ex, "Failed to log field difference for {FieldKey}", field.Key);
            }
        }

        private string? GetCurrentFieldValue(OrderDifference fieldKey, IOrder order)
        {
            try
            {
                return fieldKey switch
                {
                    OrderDifference.Status => order.GetFloristOrderStatus(),
                    OrderDifference.OriginSystem => order.GetOrderOriginSystem(),
                    OrderDifference.ExecutingFloristId => order.GetExecutingFloristId(),
                    OrderDifference.TransmitterFloristId => order.GetTransmitterFloristId(),
                    OrderDifference.DeliveryDate => order.GetDeliveryDate()?.Date.ToString("yyyy-MM-dd"),
                    OrderDifference.DeliveryMoment => order.GetDeliveryMoment(),
                    OrderDifference.DeliveryTime => order.GetDeliveryTime(),
                    OrderDifference.RecipientTitle => order.GeContactTitle(),
                    OrderDifference.RecipientFirstName => order.ShippingAddress?.FirstName,
                    OrderDifference.RecipientLastName => order.ShippingAddress?.LastName,
                    OrderDifference.RecipientPhoneNumber => order.ShippingAddress?.Mobile,
                    OrderDifference.Message => order.GetMessage(),
                    OrderDifference.Signature => order.GetSignature(),
                    OrderDifference.DeliveryCompany => order.ShippingAddress?.Company,
                    OrderDifference.DeliveryStreet => $"{order.ShippingAddress?.StreetNumber} {order.ShippingAddress?.StreetName}".Trim(),
                    OrderDifference.DeliveryCity => order.ShippingAddress?.City,
                    OrderDifference.DeliveryZipCode => order.ShippingAddress?.PostalCode,
                    OrderDifference.DeliveryCountryCode => order.ShippingAddress?.Country,
                    OrderDifference.DeliveryAdditionalInfo => order.ShippingAddress?.AdditionalAddressInfo,
                    OrderDifference.DeliveryInstructions => order.ShippingAddress?.GetComments(),
                    OrderDifference.ContactFirstName => order.ShippingAddress?.GetContactFirstName(),
                    OrderDifference.ContactLastName => order.ShippingAddress?.GetContactLastName(),
                    OrderDifference.CustomerFirstName => order.BillingAddress?.FirstName,
                    OrderDifference.CustomerLastName => order.BillingAddress?.LastName,
                    OrderDifference.CustomerEmail => order.BillingAddress?.Email,
                    OrderDifference.CustomerPhoneNumber => order.BillingAddress?.Mobile,
                    OrderDifference.CustomerCompany => order.BillingAddress?.Company,
                    OrderDifference.BillingStreet => order.BillingAddress?.StreetName,
                    OrderDifference.BillingCity => order.BillingAddress?.City,
                    OrderDifference.BillingZipCode => order.BillingAddress?.PostalCode,
                    _ => "N/A"
                };
            }
            catch (Exception ex)
            {
                logger.LogWarning(ex, "Failed to get current value for field {FieldKey}", fieldKey);
                return "ERROR_GETTING_VALUE";
            }
        }

        private static string? SerializeValue(object value)
        {
            try
            {
                if (value == null) return "NULL";

                if (value is string str) return str;

                if (value is Dictionary<string, object> dict)
                    return $"Dict[{string.Join(", ", dict.Select(kvp => $"{kvp.Key}:{kvp.Value}"))}]";

                if (value is IEnumerable<object> enumerable && value is not string)
                    return $"List[{string.Join(", ", enumerable.Select(x => x?.ToString() ?? "NULL"))}]";

                return value.ToString();
            }
            catch
            {
                return "SERIALIZATION_ERROR";
            }
        }

        private async Task AssignOrderProcess(LegacyOrderAssignedMessage message)
        {
            if (message?.Payload?.FloristIdentifier == default)
            {
                logger.LogWarning("FloristIdentifier is null");
            }
            var executorFlorist = await iFloristsRepository.GetByIdentifier(message?.Payload?.FloristIdentifier);
            if (executorFlorist == null)
            {
                logger.LogWarning("FloristIdentifier is unknown");
            }

            GetFloristCounterPerDayResult result = await GetFloristCounterPerDay(message?.Payload);
            if (result.Counter >= 0)
            {
                result.Counter++;
                string internalOrderId = result.OrderDeliveryDate.ToString("yyyyMMdd") + "-" + result.Counter.ToString();

                GlobalOrderAssigned orderAssigned = message;
                IOrder orderBeforeUpdate = null;

                StatusEnum defaultStatus = StatusEnum.ASSIGNED;

                if (orderReactorSettings?.CurrentValue?.CountryCode == "FR")
                    defaultStatus = StatusEnum.ACCEPTED;

                string[] countryCodesAddDocReceiver = new[] { "ES", "PT", "IT", "IT2" };
                string? executingFloristInvoiceUrl = null;
                if (countryCodesAddDocReceiver.Contains(orderReactorSettings?.CurrentValue?.CountryCode) && (orderReactorSettings?.CurrentValue?.SEUSetExecutingFloristInvoiceUrl ?? false))
                {
                    executingFloristInvoiceUrl = orderReactorSettings?.CurrentValue?.PFsGetOrderDocumentOrderUrlFormat
                            .Replace("{orderIdentifier}", orderAssigned.OrderIdentifier)
                            .Replace("{floristIdentifier}", executorFlorist.Id)
                            .Replace("{type}", ((int)DocTypeEnum.IT_DOC_ORDER_RECEIVER).ToString());
                }
               
                logger.LogInformation("AssignOrderProcess => HandleOrderAssigned called for order {OrderId}", orderAssigned.OrderIdentifier);
                orderBeforeUpdate = await retryHandler.Handle409Conflict(orderService.HandleOrderAssigned, orderAssigned, internalOrderId, defaultStatus, executingFloristInvoiceUrl, nameof(LegacyOrderAssignedMessage));
                if (message.Payload.FloristIdentifier == "0000990")
                {
                    if (orderBeforeUpdate != null)
                    {
                        GlobalFloristOrderPerDayModel globalFloristOrderPerDay = new GlobalFloristOrderPerDayModel
                        {
                            Counter = result.Counter,
                            FloristIdentifier = message?.Payload?.FloristIdentifier,
                            DeliveryDate = result.OrderDeliveryDate,

                        };
                        globalFloristOrderPerDay.StoreProjectedEvent(message.MessageId);
                        globalFloristOrderPerDay.SetLastUpdate();
                        globalFloristOrderPerDay.SetId();

                        await UpdateFloristCounterPerDay(globalFloristOrderPerDay);


                        if (countryCodesAddDocReceiver.Contains(orderReactorSettings?.CurrentValue?.CountryCode))
                        {
                            string url = orderReactorSettings?.CurrentValue?.PFsGetOrderDocumentOrderUrlFormat
                                .Replace("{orderIdentifier}", orderAssigned.OrderIdentifier)
                                .Replace("{floristIdentifier}", executorFlorist.Id)
                                .Replace("{type}", ((int)DocTypeEnum.IT_DOC_ORDER_RECEIVER).ToString());

                            executorFlorist.Documents.Add(new Document
                            {
                                CTOrderId = orderAssigned.OrderIdentifier,
                                DocType = DocTypeEnum.IT_DOC_ORDER_RECEIVER,
                                FileExtension = "pdf",
                                FileName = orderBeforeUpdate.OrderNumber + ".pdf",
                                OctopusOrderId = orderBeforeUpdate.OrderNumber,
                                OrderReference = orderAssigned.OrderIdentifier,
                                Month = orderBeforeUpdate.CreatedAt.Month,
                                Year = orderBeforeUpdate.CreatedAt.Year,
                                Url = url
                            });

                            await iFloristsRepository.SaveDocuments(executorFlorist);
                        }

                        OrderNewHistoryRecordMessageBuilder builder = new OrderNewHistoryRecordMessageBuilder();
                        builder.AddCommerceToolsID(message.Payload.OrderIdentifier)
                            .AddOrderNumber(orderBeforeUpdate.OrderNumber)
                            .AddLegacyOrderNumber(orderBeforeUpdate.GetLegacyOrderNumber())
                            .AddInitialOrderStatus(orderBeforeUpdate.GetFloristOrderStatus())
                            .AddExecutingFloristId(orderBeforeUpdate.GetExecutingFloristId())
                            .AddOrderAmount(orderBeforeUpdate.GetTotalItemsPrice() + orderBeforeUpdate.GetDeliveryPrice())
                            .AddRequest(message.Serialize())
                        .AddOrderAction("ASSIGNED DONE")
                            .AddCtOrderPreUpdate(orderBeforeUpdate?.Serialize(SerializerType.CommerceTools, serializerService))
                            .AddMessage($"Order {orderBeforeUpdate.OrderNumber} has been updated as ASSIGNED to florist {message?.Payload?.FloristIdentifier}")
                            .Build();
                        await kafkaPublisher.Publish(builder.Build(), kafkaTopicsSettings.CurrentValue.Order);

                        logger.LogInformation("The message with id {id} has been handled in the process {process} on message {message} for order {Id}. Payload : {payload}", message.MessageId, nameof(SychronizeProcess), nameof(LegacyOrderAssignedMessage), message.GetMessageKey(), message.Serialize());
                    }
                    else
                    {
                        logger.LogWarning("The message with id {id} failed in the process {process} on message {message} for order {Id}. Payload : {payload}", message.MessageId, nameof(SychronizeProcess), nameof(LegacyOrderAssignedMessage), message.GetMessageKey(), message.Serialize());
                    }
                }
            }
            else
            {
                logger.LogWarning("The message with id {id} failed in the process {process} on message {message} for order {Id} becase evaluated internalOrderId is empty. Payload : {payload}", message.MessageId, nameof(SychronizeProcess), nameof(LegacyOrderAssignedMessage), message.GetMessageKey(), message.Serialize());
            }
        }

        public async Task SychronizeProcess(OrderReclamationMessage message)
        {
            try
            {
                logger.LogInformation("{Message} : Synchronized {Name}", nameof(OrderReclamationMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }

                var claim = new OrderClaim
                {
                    Id = message.Payload.DisputeId.ToString(),
                    DeliveryDate = message.Payload.DeliveryDate,
                    FloristId = message.Payload.FloristId,
                    OrderId = message.Payload.OrderId,
                    ProductCode = message.Payload.ProductId,
                    ProductSize = message.Payload.ProductSizeId,
                    ProductStyle = message.Payload.ProductStyleId,
                    ReclamationCode = message.Payload.ReclamationCode,
                    ReclamationReason = message.Payload.ReclamationReason
                };

                await _orderClaimsRepository.ReplaceOneAsync(claim);

                

            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(OrderReclamationMessage), message?.Payload?.OrderId, message?.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(OrderReclamationMessage)} for order {message?.Payload?.OrderId}. Payload : {message?.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }

        public async Task SychronizeProcess(OrderCustomerFeedbackMessage message)
        {
            try
            {
                logger.LogInformation("{Message} : Synchronized {Name}", nameof(OrderCustomerFeedbackMessage), message.GetMessageKey());

                if (message.Payload == default)
                {
                    logger.LogWarning("The payload is null");
                    return;
                }

                var customerFeedback = new OrderCustomerFeedback
                {
                    Id = message.Payload.NpsOderId,
                    DeliveryDate = message.Payload.DeliveryDate,
                    FloristId = message.Payload.FloristId,
                    OrderId = message.Payload.OrderId,
                    BusinessUnit = message.Payload.BusinessUnit,
                    Comment = message.Payload.Comment,
                    OrderDate = message.Payload.OrderDate,
                    Reason = message.Payload.Reason,
                    ResponseDate = message.Payload.ResponseDate,
                    Score = message.Payload.Score
                };

                await _orderCustomerFeedbackRepository.ReplaceOneAsync(customerFeedback);



            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to process {process} on message {message} for order {orderIdentifier}. Payload : {payload}. Exception: {Exmessage}", nameof(SychronizeProcess), nameof(OrderCustomerFeedbackMessage), message?.Payload?.OrderId, message?.Serialize(), ex.ToString());
                await (slackAlertService?.SendErrorAlertAsync($"Failed to process {nameof(SychronizeProcess)} on message {nameof(OrderCustomerFeedbackMessage)} for order {message?.Payload?.OrderId}. Payload : {message?.Serialize()}. Exception: {ex}", ex) ?? Task.CompletedTask);
            }
        }
    }
}
