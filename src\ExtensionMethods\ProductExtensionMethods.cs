using commercetools.Sdk.Api.Models.Products;
using ITF.SharedModels.DataModels.Order;

namespace IT.SharedLibraries.CT.ExtensionMethods
{
    public static class ProductExtensionMethods
    {
        public static bool GetAllowPriceDifferentFromVariantsOne(this IProduct product)
        {
            if (product != null && product.MasterData != null && product.MasterData.Current != null && product.MasterData.Current.MasterVariant != null)
            {
                return product.MasterData.Current.MasterVariant.GetAllowPriceDifferentFromVariantsOne();
            }
            return false;
        }

        public static IProductVariant? GetMatchingVariant(this IProduct product, string variantKey)
        {
            if (product == null || variantKey == null)
                return null;
            if (product.MasterData.Current.MasterVariant.Key == variantKey)
                return product.MasterData.Current.MasterVariant;
            else
            {
                IProductVariant? variant = product.MasterData.Current.Variants.FirstOrDefault(pv => pv.Key == variantKey);
                if (variant == null)
                    return null;
                else
                    return variant;
            }
        }
    }

    public static class GlobalProductExtensionMethods
    {
        /// <summary>
        /// Extracts the size of the product by getting the part between the first and last hyphen in the VariantKey.
        /// </summary>
        /// <param name="orderProduct">The extension object on the GlobalOrderProduct with VariantKey (e.g., "1VAFR-6-FR")</param>
        /// <returns>The Size part between hyphens (Size of the product), or null if extraction fails</returns>
        public static int? ExtractProductSizeFromVariantKey(this GlobalOrderProduct orderProduct)
        {
            if (string.IsNullOrEmpty(orderProduct?.VariantKey))
                return null;
            try
            {
                // Find positions of first and last hyphens
                int firstHyphenIndex = orderProduct.VariantKey.IndexOf('-');
                int lastHyphenIndex = orderProduct.VariantKey.LastIndexOf('-');

                // Validate that there are at least two hyphens and they're not the same
                if (firstHyphenIndex < 0 || lastHyphenIndex < 0 || firstHyphenIndex == lastHyphenIndex)
                    return null;

                // Extract the size part (start after first hyphen, length is the difference minus 1)
                int startIndex = firstHyphenIndex + 1;
                int length = lastHyphenIndex - startIndex;

                // Ensure valid substring parameters
                if (length <= 0 || startIndex + length > orderProduct.VariantKey.Length)
                    return null;

                string sizePart = orderProduct.VariantKey.Substring(startIndex, length);

                // Validate the extracted string contains only digits
                if (string.IsNullOrWhiteSpace(sizePart) || !sizePart.All(char.IsDigit))
                    return null;

                // Attempt to convert to integer with overflow protection
                if (int.TryParse(sizePart, out int result))
                    return result;
                else
                    return null;
            }
            catch (Exception)
            {
                return null;
            }
        }

        /// <summary>
        /// Transform the VariantKey From the product to the VariantKey waiting for CT --> mapping for "A la tige" products.
        /// </summary>
        /// <param name="orderProduct">The extension object on the GlobalOrderProduct with VariantKey (e.g., "1VAFR-6-FR")</param>
        /// <returns>The VariantKey mapped for CT , or null if mapping fails</returns>
        public static string? TransfromVariantKeyForCTWithAlaTigeProduct(this GlobalOrderProduct orderProduct)
        {
            if (string.IsNullOrEmpty(orderProduct?.VariantKey))
                return null;
            try
            {
                // Find positions of first and last hyphens
                int firstHyphenIndex = orderProduct.VariantKey.IndexOf('-');
                int lastHyphenIndex = orderProduct.VariantKey.LastIndexOf('-');

                // Validate that there are at least two hyphens and they're not the same
                if (firstHyphenIndex < 0 || lastHyphenIndex < 0 || firstHyphenIndex == lastHyphenIndex)
                    return null;

                // Extract the parts before first hyphen, and after last hyphen
                string firstPart = orderProduct.VariantKey[..(firstHyphenIndex + 1)]; // Include the hyphen
                string lastPart = orderProduct.VariantKey[lastHyphenIndex..]; // Includes the last hyphen

                // Construct the new string
                return firstPart + "xxx" + lastPart;
            }
            catch (Exception)
            {
                return null;
            }
        }
    }
    public static class ProductVariantExtensionMethods
    {
        public static bool HasExpiredPriceMatchingProduct(this IProductVariant variant, GlobalOrderProduct product , int frSizeProduct = 1)
        {
            return variant.Prices != null &&
                   variant.Prices.Any(price =>
                       price.GetPrice(frSizeProduct) == product.Price && price.IsExpired());
        }

        public static bool HasPriceMatchingProduct(this IProductVariant variant, GlobalOrderProduct product, int frSizeProduct = 1)
        {
            return variant.Prices != null &&
                   variant.Prices.Any(price =>
                       price.GetPrice(frSizeProduct) == product.Price);
        }
    }

    public static class ProductProjectionExtensionMethods
    {


        public static List<string> GetPriceChannelIds(this ProductProjection product)
        {
            List<string> channels = new();
            IProductVariant? ctVariant = product?.MasterVariant;
            if (ctVariant?.Price != null && ctVariant?.Price?.Channel != null)
            {
                if (!channels.Contains(ctVariant.Price.Channel.Id))
                    channels.Add(ctVariant.Price.Channel.Id);
            }
            if (ctVariant?.Prices != null && ctVariant?.Prices.Count > 0)
            {
                foreach (var price in ctVariant.Prices)
                {
                    if (price.Channel != null && !channels.Contains(price.Channel.Id))
                    {
                        channels.Add(price.Channel.Id);
                    }
                }
            }

            if (product?.Variants != null)
            {
                foreach (var variant in product.Variants)
                {
                    if (variant?.Price != null && variant?.Price?.Channel != null)
                    {
                        if (!channels.Contains(variant.Price.Channel.Id))
                            channels.Add(variant.Price.Channel.Id);
                    }
                    if (variant?.Prices != null && variant?.Prices.Count > 0)
                    {
                        foreach (var price in variant.Prices)
                        {
                            if (price.Channel != null && !channels.Contains(price.Channel.Id))
                            {
                                channels.Add(price.Channel.Id);
                            }
                        }
                    }
                }
            }

            return channels;
        }
    }
}
