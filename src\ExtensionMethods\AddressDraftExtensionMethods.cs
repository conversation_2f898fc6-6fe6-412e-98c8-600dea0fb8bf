﻿using commercetools.Sdk.Api.Models.Common;
using commercetools.Sdk.Api.Models.Types;
using IT.SharedLibraries.CT.CustomAttributes;
using ITF.SharedModels.Group.Enums;
using System;

namespace IT.SharedLibraries.CT.ExtensionMethods
{
    public static class AddressDraftExtensionMethods
    {
        private static void CheckCustom(this IAddressDraft addressDraft)
        {
            if (addressDraft.Custom == null)
            {
                addressDraft.Custom = new CustomFieldsDraft
                {
                    Type = new TypeResourceIdentifier
                    {
                        Key = "address-custom"
                    },
                    Fields = new FieldContainer()
                };
            }
        }
        public static void SetFiscalCode(this IAddressDraft addressDraft, string fiscalCode)
        {
            if (addressDraft != null)
            {
                addressDraft.CheckCustom();
                addressDraft.Custom.Fields.Add(CtOrderCustomAttributesNames.BillingAddress.FISCAL_CODE, fiscalCode);
            }
        }

        public static void SetInvoiceFirstName(this IAddressDraft addressDraft, string invoiceFirstName)
        {
            if (addressDraft != null && !string.IsNullOrEmpty(invoiceFirstName))
            {
                addressDraft.CheckCustom();
                addressDraft.Custom.Fields.Add(CtOrderCustomAttributesNames.BillingAddress.INVOICE_FIRSTNAME, invoiceFirstName);
            }
        }

        public static void SetInvoiceLastName(this IAddressDraft addressDraft, string invoiceLastName)
        {
            if (addressDraft != null && !string.IsNullOrEmpty(invoiceLastName))
            {
                addressDraft.CheckCustom();
                addressDraft.Custom.Fields.Add(CtOrderCustomAttributesNames.BillingAddress.INVOICE_LASTNAME, invoiceLastName);
            }
        }


        public static void SetDate(this IAddressDraft addressDraft, DateTime date)
        {
            if (addressDraft != null)
            {
                addressDraft.CheckCustom();
                addressDraft.Custom.Fields.Add(CtOrderCustomAttributesNames.ShippingAddress.DATE, date.ToString("yyyy-MM-dd"));
            }
        }
        public static void SetTime(this IAddressDraft addressDraft, string time)
        {
            if (addressDraft != null)
            {
                addressDraft.CheckCustom();
                addressDraft.Custom.Fields.Add(CtOrderCustomAttributesNames.ShippingAddress.TIME, time);
            }
        }
        public static void SetMoment(this IAddressDraft addressDraft, MomentEnum moment)
        {
            if (addressDraft != null)
            {
                addressDraft.CheckCustom();
                addressDraft.Custom.Fields.Add(CtOrderCustomAttributesNames.ShippingAddress.MOMENT, ((int)moment).ToString());
            }
        }
        public static void SetLatitude(this IAddressDraft addressDraft, double lat)
        {
            if (addressDraft != null)
            {
                addressDraft.CheckCustom();
                addressDraft.Custom.Fields.Add(CtOrderCustomAttributesNames.ShippingAddress.LATITUDE, lat);
            }
        }
        public static void SetLongitude(this IAddressDraft addressDraft, double longit)
        {
            if (addressDraft != null)
            {
                addressDraft.CheckCustom();
                addressDraft.Custom.Fields.Add(CtOrderCustomAttributesNames.ShippingAddress.LONGITUDE, longit);
            }
        }
        public static void SetComments(this IAddressDraft addressDraft, string comments)
        {
            if (addressDraft != null)
            {
                addressDraft.CheckCustom();
                addressDraft.Custom.Fields.Add(CtOrderCustomAttributesNames.ShippingAddress.COMMENTS, comments);
            }
        }
        public static void SetContactTitle(this IAddressDraft addressDraft, string contactTitle)
        {
            if (addressDraft != null)
            {
                addressDraft.CheckCustom();
                addressDraft.Custom.Fields.Add(CtOrderCustomAttributesNames.Order.CONTACT_TITLE, contactTitle);
            }
        }
        public static void SetContactFirstName(this IAddressDraft addressDraft, string contactFirstName)
        {
            if (addressDraft != null)
            {
                addressDraft.CheckCustom();
                addressDraft.Custom.Fields.Add(CtOrderCustomAttributesNames.Order.CONTACT_FIRST_NAME, contactFirstName);
            }
        }
        public static void SetContactLastName(this IAddressDraft addressDraft, string contactLastName)
        {
            if (addressDraft != null)
            {
                addressDraft.CheckCustom();
                addressDraft.Custom.Fields.Add(CtOrderCustomAttributesNames.Order.CONTACT_LAST_NAME, contactLastName);
            }
        }

    }
}
