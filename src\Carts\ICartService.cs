﻿using commercetools.Sdk.Api.Models.Carts;
using System.Threading.Tasks;

namespace IT.SharedLibraries.CT.Carts
{
    public interface ICartService
    {
        Task<ICart> Create(ICartDraft newInstance);
        Task<ICart> Update(ICart oldTInstance, ICart newInstance);
        Task<ICart> UpdateInatCartAddShippingInfo(ICart oldTInstance, ICart newInstance, decimal? inatOrderShippingTaxRate, decimal? inatOrderShippingTaxAmount, string deliveryCountryCode, string currencyCode);
        Task<ICart> GetById(string id);
    }
}
