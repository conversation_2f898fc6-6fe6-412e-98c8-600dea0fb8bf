﻿using commercetools.Sdk.Api.Models.Common;
using commercetools.Sdk.Api.Models.Products;
using IT.SharedLibraries.CT.ExtensionMethods;
using ITF.SharedModels.DataModels.Order;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Xunit;

namespace IT.SharedLibraries.CT.UnitTests.Extensions
{
    public class ProductVariantTest
    {
        [Theory]
        [InlineData(12.34, true, false, true)]    // Prix = produit, expiré, Prices non null => true
        [InlineData(12.34, false, false, false)]  // Prix = produit, pas expiré, Prices non null => false
        [InlineData(10.00, true, false, false)]   // Prix ≠ produit, expiré, Prices non null => false
        [InlineData(10.00, false, false, false)]  // Prix ≠ produit, pas expiré, Prices non null => false
        [InlineData(0.0, false, true, false)]     // Prices est null => false
        public void HasExpiredPriceMatchingProduct_ReturnsExpectedResult(decimal priceValue, bool expired, bool isNullPrices, bool expectedResult)
        {
            // Arrange
            var product = new GlobalOrderProduct { Price = 12.34m };

            IList<IPrice> prices = null;

            if (!isNullPrices)
            {
                var moneyMock = new Mock<ITypedMoney>();
                moneyMock.Setup(m => m.CentAmount).Returns((int)(priceValue * 100));
                moneyMock.Setup(m => m.FractionDigits).Returns(2);

                var priceMock = new Mock<IPrice>();
                priceMock.Setup(p => p.Value).Returns(moneyMock.Object);
                priceMock.Setup(p => p.ValidUntil).Returns(expired ? DateTime.Now.AddDays(-1) : DateTime.Now.AddDays(1));

                prices = new List<IPrice> { priceMock.Object };
            }

            var variantMock = new Mock<IProductVariant>();
            variantMock.Setup(v => v.Prices).Returns(prices);

            // Act
            var result = variantMock.Object.HasExpiredPriceMatchingProduct(product);

            // Assert
            Assert.Equal(expectedResult, result);
        }
    }
}
